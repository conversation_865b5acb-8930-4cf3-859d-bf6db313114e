(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5949],{33217:function(e,s,t){Promise.resolve().then(t.bind(t,70371))},70371:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return P}});var r=t(57437),a=t(2265),i=t(99376),n=t(29501),d=t(87758),l=t(46714),c=t(3371),o=t(67654),m=t(12381),h=t(40279),p=t(66840),u=a.forwardRef((e,s)=>(0,r.jsx)(p.WV.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));u.displayName="Label";var x=t(93448);function g(e){let{className:s,...t}=e;return(0,r.jsx)(u,{"data-slot":"label",className:(0,x.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}var f=t(15863),y=t(40340),j=t(88226),N=t(83774),v=t(4139);function b(e){let{onLocationDetected:s,onError:t,disabled:i}=e,[n,d]=(0,a.useState)(!1),l=async()=>{d(!0);try{let e=await (0,v.vW)();s({latitude:e.latitude,longitude:e.longitude,city:"",state:"",country:"India"}),t("Location detected! Please enter your address details manually.")}catch(e){console.error("Location detection error:",e),t(e instanceof Error?e.message:"Failed to detect location")}finally{d(!1)}};return(0,r.jsx)(m.z,{type:"button",variant:"outline",onClick:l,disabled:i||n,className:"w-full mb-4",children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Detecting Location..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"Detect My Location"]})})}function w(e){let{selectedState:s,selectedCity:t,onStateChange:i,onCityChange:n,stateError:d,cityError:l}=e,[c]=(0,a.useState)((0,v.Rh)()),[o,m]=(0,a.useState)([]);return(0,a.useEffect)(()=>{if(s){let e=(0,v.nY)(s);m(e),t&&!e.includes(t)&&n("")}else m([]),n("")},[s,t,n]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"state",children:"State"}),(0,r.jsxs)("select",{id:"state",value:s,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2c2c27] ".concat(d?"border-red-300":"border-gray-300"),children:[(0,r.jsx)("option",{value:"",children:"Select State"}),c.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),d&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"city",children:"City"}),(0,r.jsxs)("select",{id:"city",value:t,onChange:e=>n(e.target.value),disabled:!s||0===o.length,className:"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2c2c27] ".concat(l?"border-red-300":"border-gray-300"," ").concat(s&&0!==o.length?"":"bg-gray-100 cursor-not-allowed"),children:[(0,r.jsx)("option",{value:"",children:"Select City"}),o.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),l&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:l}),s&&0===o.length&&(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"No cities available for selected state"})]})]})}function P(){var e,s;let t=(0,i.useRouter)(),{isAuthenticated:p,isLoading:u}=(0,c.O)(),x=(0,d.useLocalCartStore)(),N=(0,l.p)(),[P,S]=(0,a.useState)(!1),[F,E]=(0,a.useState)(null),{register:C,handleSubmit:A,watch:k,setValue:q,formState:{errors:I}}=(0,n.cI)({mode:"onChange"});(0,a.useEffect)(()=>{C("state",{required:"State is required"}),C("city",{required:"City is required"})},[C]);let L=k("pincode"),_=k("state"),D=k("city");(0,a.useEffect)(()=>{if(!p){t.push("/sign-in");return}if(0===x.items.length){t.push("/");return}N.setCart(x.items)},[x.items,t,p]),(0,a.useEffect)(()=>{(0,o.mw)()},[]),(0,a.useEffect)(()=>{L&&6===L.length&&p&&N.fetchShippingRates(L)},[L,p]),(0,a.useEffect)(()=>{L&&6===L.length&&(async()=>{try{let e=await (0,v.v8)(L);e.state&&q("state",e.state),e.city&&q("city",e.city),E(null)}catch(e){console.error("Error fetching location from pincode:",e)}})()},[L,q]);let z=async e=>{let s={firstName:e.firstName,lastName:e.lastName,address1:e.address1,address2:e.address2,city:e.city,state:e.state,pincode:e.pincode,phone:e.phone};N.setShippingAddress(s)},O=async()=>{if(!N.shippingAddress){N.setError("Please fill in your shipping address");return}if(!N.selectedShipping){N.setError("Please select a shipping method");return}if(0===N.cart.length){N.setError("Your cart is empty");return}if(N.finalAmount<=0){N.setError("Invalid order amount");return}S(!0),N.setProcessingPayment(!0),N.setError(null);try{let e="rzp_live_H1Iyl4j48eSFYj";if(!e)throw Error("Payment gateway not configured. Please contact support.");console.log("Creating Razorpay order for amount:",N.finalAmount);let s=await (0,o.wi)(N.finalAmount,"order_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),{customer_phone:N.shippingAddress.phone,customer_name:"".concat(N.shippingAddress.firstName," ").concat(N.shippingAddress.lastName),shipping_method:N.selectedShipping.name});console.log("Razorpay order created:",s.id),await (0,o.Jr)({key:e,amount:s.amount,currency:s.currency,name:"Ankkor",description:"Order Payment - ".concat(N.cart.length," item(s)"),order_id:s.id,handler:async e=>{console.log("Payment successful, verifying...",e),N.setError(null);try{let s=await (0,o.t6)(e,{address:N.shippingAddress,cartItems:N.cart,shipping:N.selectedShipping});if(console.log("Payment verification result:",s),s.success)x.clearCart(),N.clearCheckout(),t.push("/order-confirmed?id=".concat(s.orderId));else throw Error(s.message||"Payment verification failed")}catch(e){console.error("Payment verification error:",e),N.setError(e instanceof Error?e.message:"Payment verification failed. Please contact support if amount was deducted.")}finally{S(!1),N.setProcessingPayment(!1)}},prefill:{name:"".concat(N.shippingAddress.firstName," ").concat(N.shippingAddress.lastName),contact:N.shippingAddress.phone},theme:{color:"#2c2c27"},modal:{ondismiss:()=>{console.log("Payment modal dismissed"),S(!1),N.setProcessingPayment(!1)}}})}catch(i){var e,s,r,a;console.error("Payment error:",i);let t="Payment failed. Please try again.";(null===(e=i.message)||void 0===e?void 0:e.includes("not configured"))?t=i.message:(null===(s=i.message)||void 0===s?void 0:s.includes("network"))||(null===(r=i.message)||void 0===r?void 0:r.includes("fetch"))?t="Network error. Please check your connection and try again.":(null===(a=i.message)||void 0===a?void 0:a.includes("amount"))?t="Invalid amount. Please refresh and try again.":i.message&&(t=i.message),N.setError(t)}finally{S(!1),N.setProcessingPayment(!1)}};return u?(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[(0,r.jsx)(f.Z,{className:"h-8 w-8 animate-spin"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading..."})]})}):p&&0!==x.items.length?(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-serif mb-8",children:"Checkout"}),N.error&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded",children:N.error}),F&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 text-blue-700 rounded",children:F}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,r.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,r.jsx)(y.Z,{className:"mr-2 h-5 w-5"}),"Shipping Address"]}),(0,r.jsx)(b,{onLocationDetected:e=>{E(null)},onError:e=>{E(e)},disabled:P}),(0,r.jsxs)("form",{onSubmit:A(z),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"firstName",children:"First Name"}),(0,r.jsx)(h.I,{id:"firstName",...C("firstName",{required:"First name is required"}),className:I.firstName?"border-red-300":""}),I.firstName&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I.firstName.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"lastName",children:"Last Name"}),(0,r.jsx)(h.I,{id:"lastName",...C("lastName",{required:"Last name is required"}),className:I.lastName?"border-red-300":""}),I.lastName&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I.lastName.message})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)(g,{htmlFor:"address1",children:"Address Line 1"}),(0,r.jsx)(h.I,{id:"address1",...C("address1",{required:"Address is required"}),className:I.address1?"border-red-300":""}),I.address1&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I.address1.message})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)(g,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),(0,r.jsx)(h.I,{id:"address2",...C("address2")})]}),(0,r.jsx)(w,{selectedState:_||"",selectedCity:D||"",onStateChange:e=>q("state",e),onCityChange:e=>q("city",e),stateError:null===(e=I.state)||void 0===e?void 0:e.message,cityError:null===(s=I.city)||void 0===s?void 0:s.message}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"pincode",children:"Pincode"}),(0,r.jsx)(h.I,{id:"pincode",...C("pincode",{required:"Pincode is required",pattern:{value:/^[0-9]{6}$/,message:"Please enter a valid 6-digit pincode"}}),className:I.pincode?"border-red-300":"",placeholder:"Enter 6-digit pincode"}),I.pincode&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I.pincode.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)(h.I,{id:"phone",...C("phone",{required:"Phone number is required"}),className:I.phone?"border-red-300":""}),I.phone&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I.phone.message})]})]}),(0,r.jsx)(m.z,{type:"submit",className:"mt-4 w-full bg-[#2c2c27] hover:bg-[#3c3c37] text-white",children:"Save Address & Continue"})]})]}),(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,r.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,r.jsx)(y.Z,{className:"mr-2 h-5 w-5"}),"Shipping Options"]}),N.isLoadingShipping?(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,r.jsx)(f.Z,{className:"h-6 w-6 animate-spin mr-2"}),(0,r.jsx)("span",{children:"Loading shipping options..."})]}):0===N.shippingOptions.length?(0,r.jsx)("div",{className:"text-gray-500 py-4",children:L&&6===L.length?"No shipping options available for this pincode":"Enter a valid pincode to see shipping options"}):(0,r.jsx)("div",{className:"space-y-3",children:N.shippingOptions.map(e=>{var s,t;return(0,r.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===(s=N.selectedShipping)||void 0===s?void 0:s.id)===e.id?"border-[#2c2c27] bg-gray-50":"border-gray-200 hover:border-gray-300"),onClick:()=>N.setSelectedShipping(e),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",name:"shipping",checked:(null===(t=N.selectedShipping)||void 0===t?void 0:t.id)===e.id,onChange:()=>N.setSelectedShipping(e),className:"mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:e.name}),e.description&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),e.estimatedDays&&(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Estimated delivery: ",e.estimatedDays]})]})]})}),(0,r.jsx)("div",{className:"text-lg font-medium",children:0===e.cost?"Free":"₹".concat(e.cost.toFixed(2))})]})},e.id)})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,r.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,r.jsx)(j.Z,{className:"mr-2 h-5 w-5"}),"Payment"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center p-4 border rounded-lg",children:[(0,r.jsx)("input",{type:"radio",id:"razorpay",name:"payment",checked:!0,readOnly:!0,className:"mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"razorpay",className:"font-medium",children:"Razorpay"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Pay securely with credit card, debit card, UPI, or net banking"})]})]}),(0,r.jsx)(m.z,{onClick:O,className:"w-full py-6 bg-[#2c2c27] hover:bg-[#3c3c37] text-white",disabled:P||!N.shippingAddress||!N.selectedShipping||N.isProcessingPayment,children:P||N.isProcessingPayment?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing Payment..."]}):"Proceed to Pay - ₹".concat(N.finalAmount.toFixed(2))})]})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm sticky top-8",children:[(0,r.jsx)("h2",{className:"text-xl font-medium mb-4",children:"Order Summary"}),(0,r.jsxs)("div",{className:"space-y-4",children:[N.cart.map(e=>{var s;return(0,r.jsxs)("div",{className:"flex gap-4 py-2 border-b",children:[(null===(s=e.image)||void 0===s?void 0:s.url)&&(0,r.jsx)("div",{className:"relative h-16 w-16 bg-gray-100 flex-shrink-0",children:(0,r.jsx)("img",{src:e.image.url,alt:e.name,className:"h-full w-full object-cover rounded"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["₹","string"==typeof e.price?parseFloat(e.price).toFixed(2):e.price.toFixed(2)," \xd7 ",e.quantity]})]}),(0,r.jsxs)("div",{className:"text-right",children:["₹",("string"==typeof e.price?parseFloat(e.price)*e.quantity:e.price*e.quantity).toFixed(2)]})]},e.id)}),(0,r.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Subtotal"}),(0,r.jsxs)("span",{children:["₹",N.subtotal.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Shipping"}),(0,r.jsx)("span",{children:N.selectedShipping?0===N.selectedShipping.cost?"Free":"₹".concat(N.selectedShipping.cost.toFixed(2)):"TBD"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-medium pt-2 border-t",children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsxs)("span",{children:["₹",N.finalAmount.toFixed(2)]})]})]})]})]})})]})]}):null}}}]);