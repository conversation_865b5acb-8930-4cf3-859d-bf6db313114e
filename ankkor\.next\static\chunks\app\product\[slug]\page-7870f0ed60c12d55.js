(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1599],{54955:function(e,t,s){Promise.resolve().then(s.bind(s,11763))},11763:function(e,t,s){"use strict";s.d(t,{default:function(){return v}});var a=s(57437),l=s(2265),c=s(33145),r=s(43886),n=s(87758),i=s(12381),o=s(18686),d=s(21047),u=s(99397);let x=(0,s(39763).Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);var m=s(75395),p=s(42449),v=e=>{var t;let{product:s}=e,[v,h]=(0,l.useState)(0),[g,j]=(0,l.useState)(1),[f,y]=(0,l.useState)(null),[N,S]=(0,l.useState)({}),[b,k]=(0,l.useState)(!1),w=(0,n.useLocalCartStore)(),{openCart:C}=(0,o.j)(),{id:U,databaseId:T,name:_,description:E,shortDescription:I,price:L,regularPrice:O,onSale:A,stockStatus:M,image:Z,galleryImages:q,attributes:z,type:D,variations:Q}=s,{stockData:P,isConnected:B}=function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],[s,a]=(0,l.useState)({}),c=(0,l.useCallback)(t=>{t.productId===e&&a({stockStatus:t.stockStatus,stockQuantity:t.stockQuantity,availableForSale:t.availableForSale,lastUpdated:t.timestamp})},[e]),{isConnected:r,error:n}=function(){let{productIds:e=[],onStockUpdate:t,enabled:s=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[a,c]=(0,l.useState)(!1),[r,n]=(0,l.useState)(null),[i,o]=(0,l.useState)(null),d=(0,l.useCallback)(()=>{if(!s||0===e.length)return;let a=new URLSearchParams({products:e.join(",")}),l=new EventSource("/api/stock-updates?".concat(a));return l.onopen=()=>{console.log("Stock updates stream connected"),c(!0),o(null)},l.onmessage=e=>{try{let s=JSON.parse(e.data);n(s),"stock_update"===s.type&&(console.log("Stock update received:",s),null==t||t(s))}catch(e){console.error("Error parsing stock update:",e)}},l.onerror=e=>{console.error("Stock updates stream error:",e),c(!1),o("Connection to stock updates failed"),setTimeout(()=>{l.readyState===EventSource.CLOSED&&d()},5e3)},l},[e,t,s]);return(0,l.useEffect)(()=>{let e=d();return()=>{e&&(e.close(),c(!1))}},[d]),{isConnected:a,lastUpdate:r,error:i,reconnect:d}}({productIds:[e],onStockUpdate:c,enabled:t});return{stockData:s,isConnected:r,error:n}}((null==T?void 0:T.toString())||"",!0),F=P.stockStatus||M,H=P.stockQuantity,R="VARIABLE"===D,$=[(null==Z?void 0:Z.sourceUrl)?{sourceUrl:Z.sourceUrl,altText:Z.altText||_}:null,...(null==q?void 0:q.nodes)||[]].filter(Boolean),J=(e,t)=>{if(S(s=>({...s,[e]:t})),R&&(null==Q?void 0:Q.nodes)){var s;let a={...N,[e]:t};if(null==z?void 0:null===(s=z.nodes)||void 0===s?void 0:s.every(e=>a[e.name])){let e=Q.nodes.find(e=>e.attributes.nodes.every(e=>{let t=a[e.name];return e.value===t}));e?y(e):y(null)}}},K=async()=>{k(!0);try{var e,t;let s={productId:T.toString(),quantity:g,name:_,price:(null==f?void 0:f.price)||L,image:{url:(null===(e=$[0])||void 0===e?void 0:e.sourceUrl)||"",altText:(null===(t=$[0])||void 0===t?void 0:t.altText)||_}};await w.addToCart(s),C()}catch(e){console.error("Error adding product to cart:",e)}finally{k(!1)}},V="IN_STOCK"!==(F||M)&&"instock"!==(F||M),W=!R||R&&f;return(0,a.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:(null===(t=$[v])||void 0===t?void 0:t.sourceUrl)&&(0,a.jsx)(c.default,{src:$[v].sourceUrl,alt:$[v].altText||_,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),$.length>1&&(0,a.jsx)("div",{className:"grid grid-cols-5 gap-2",children:$.map((e,t)=>(0,a.jsx)("button",{onClick:()=>h(t),className:"relative aspect-square bg-[#f4f3f0] ".concat(v===t?"ring-2 ring-[#2c2c27]":""),children:(0,a.jsx)(c.default,{src:e.sourceUrl,alt:e.altText||"".concat(_," - Image ").concat(t+1),fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},t))})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:_}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-xl font-medium text-[#2c2c27]",children:((null==f?void 0:f.price)||L).toString().includes("₹")||((null==f?void 0:f.price)||L).toString().includes("$")||((null==f?void 0:f.price)||L).toString().includes("€")||((null==f?void 0:f.price)||L).toString().includes("\xa3")?(null==f?void 0:f.price)||L:"₹".concat((null==f?void 0:f.price)||L)}),A&&O&&(0,a.jsx)("span",{className:"text-sm line-through text-[#8a8778]",children:O.toString().includes("₹")||O.toString().includes("$")||O.toString().includes("€")||O.toString().includes("\xa3")?O:"₹".concat(O)})]}),I&&(0,a.jsx)("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:I}}),R&&(null==z?void 0:z.nodes)&&(0,a.jsx)("div",{className:"space-y-4",children:z.nodes.map(e=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>(0,a.jsx)("button",{onClick:()=>J(e.name,t),className:"px-4 py-2 border ".concat(N[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"),children:t},t))})]},e.name))}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>j(e=>e>1?e-1:1),disabled:g<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"px-4 py-2 border-x border-gray-300",children:g}),(0,a.jsx)("button",{onClick:()=>j(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:(0,a.jsx)(u.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Availability: "}),(0,a.jsx)("span",{className:V?"text-red-600":"text-green-600",children:V?"Out of Stock":"In Stock"}),B&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-green-600",children:[(0,a.jsx)(x,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Live"})]}),!B&&P.lastUpdated&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,a.jsx)(m.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Offline"})]})]}),null!=H&&(0,a.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:H>0?(0,a.jsxs)("span",{children:[H," items available"]}):(0,a.jsx)("span",{className:"text-red-600",children:"No items in stock"})}),P.lastUpdated&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Last updated: ",new Date(P.lastUpdated).toLocaleTimeString()]})]}),(0,a.jsxs)(r.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,a.jsxs)(i.z,{onClick:K,disabled:V||b||!W,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[(0,a.jsx)(p.Z,{className:"h-5 w-5"}),b?"Adding...":"Add to Cart"]}),R&&!W&&!V&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),E&&(0,a.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[(0,a.jsx)("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),(0,a.jsx)("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:E}})]})]})]})})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,6628,986,4754,1744],function(){return e(e.s=54955)}),_N_E=e.O()}]);