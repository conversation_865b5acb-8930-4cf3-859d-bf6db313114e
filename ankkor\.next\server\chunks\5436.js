exports.id=5436,exports.ids=[5436],exports.modules={13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},12839:(e,t,r)=>{Promise.resolve().then(r.bind(r,77321)),Promise.resolve().then(r.bind(r,40992)),Promise.resolve().then(r.bind(r,59616)),Promise.resolve().then(r.bind(r,62538)),Promise.resolve().then(r.bind(r,41071)),Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,30292)),Promise.resolve().then(r.bind(r,2861)),Promise.resolve().then(r.bind(r,69879)),Promise.resolve().then(r.bind(r,75367)),Promise.resolve().then(r.bind(r,32273)),Promise.resolve().then(r.bind(r,94001))},13121:(e,t,r)=>{Promise.resolve().then(r.bind(r,16507))},88679:(e,t,r)=>{Promise.resolve().then(r.bind(r,1125))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},35303:()=>{},16507:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(10326);r(17577);var o=r(37202),a=r(21405),n=r(95920),i=r(90434);function c({error:e,reset:t}){return s.jsx("div",{className:"min-h-screen bg-[#f8f8f5] flex items-center justify-center px-4",children:s.jsx("div",{className:"max-w-md w-full text-center",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center",children:s.jsx(o.Z,{className:"w-8 h-8 text-red-600"})})}),s.jsx("h1",{className:"text-2xl font-serif text-[#2c2c27] mb-4",children:"Something went wrong"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"We encountered an unexpected error. This has been logged and we'll look into it."}),!1,(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:t,className:"w-full bg-[#2c2c27] text-white py-3 px-6 rounded-lg hover:bg-[#8a8778] transition-colors duration-200 flex items-center justify-center gap-2",children:[s.jsx(a.Z,{className:"w-4 h-4"}),"Try again"]}),(0,s.jsxs)(i.default,{href:"/",className:"w-full bg-gray-100 text-[#2c2c27] py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center gap-2",children:[s.jsx(n.Z,{className:"w-4 h-4"}),"Go home"]})]}),s.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Need help?"," ",s.jsx(i.default,{href:"/customer-service/contact",className:"text-[#2c2c27] hover:text-[#8a8778] underline",children:"Contact support"})]})})]})})})}},1125:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(10326);r(17577);var o=r(37202),a=r(21405);function n({error:e,reset:t}){return s.jsx("html",{children:s.jsx("body",{children:s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center px-4",children:s.jsx("div",{className:"max-w-md w-full text-center",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center",children:s.jsx(o.Z,{className:"w-8 h-8 text-red-600"})})}),s.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Application Error"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"A critical error occurred. Please try refreshing the page."}),!1,(0,s.jsxs)("button",{onClick:t,className:"w-full bg-gray-900 text-white py-3 px-6 rounded-lg hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center gap-2",children:[s.jsx(a.Z,{className:"w-4 h-4"}),"Try again"]})]})})})})})}},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(10326);r(17577);var o=r(33265);let a=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),n=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(a,{})});function i(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(n,{})})}},59616:(e,t,r)=>{"use strict";function s(){return null}r.d(t,{default:()=>s}),r(17577),r(30292)},69879:(e,t,r)=>{"use strict";function s(){return null}r.d(t,{default:()=>s}),r(17577)},77321:(e,t,r)=>{"use strict";r.d(t,{default:()=>c,j:()=>i});var s=r(10326),o=r(17577),a=r(86806);let n=(0,o.createContext)(void 0),i=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},c=({children:e})=>{let t=(0,a.rY)(),[r,i]=(0,o.useState)(!1),c={openCart:()=>i(!0),closeCart:()=>i(!1),toggleCart:()=>i(e=>!e),isOpen:r,itemCount:t.itemCount};return s.jsx(n.Provider,{value:c,children:e})}},40992:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(10326),o=r(17577);let a=(0,r(33265).default)(async()=>{},{loadableGenerated:{modules:["components\\cart\\CartWrapper.tsx -> ./Cart"]},ssr:!1,loading:()=>null});function n(){let[e,t]=(0,o.useState)(!1);return e?s.jsx(a,{}):null}},62538:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(10326),o=r(17577);let a=(0,r(33265).default)(async()=>{},{loadableGenerated:{modules:["components\\layout\\FooterWrapperSSR.tsx -> ./FooterWrapper"]},ssr:!1,loading:()=>null});function n(){let[e,t]=(0,o.useState)(!1);return e?s.jsx(a,{}):null}},41071:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(10326),o=r(17577);let a=(0,r(33265).default)(async()=>{},{loadableGenerated:{modules:["components\\layout\\NavbarWrapperSSR.tsx -> ./NavbarWrapper"]},ssr:!1,loading:()=>null});function n(){let[e,t]=(0,o.useState)(!1);return e?s.jsx(a,{}):null}},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>c,O:()=>i});var s=r(10326),o=r(17577),a=r(94001);let n=(0,o.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:async()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),i=()=>(0,o.useContext)(n),c=({children:e})=>{let t=(0,a.a)(),r=async e=>{await t.login(e.email,e.password)},o=async e=>{await t.register(e)},i=async()=>{await t.logout()},c=async e=>await t.updateProfile(e),l=async()=>{await t.refreshSession()},d={customer:t.user,isLoading:t.isLoading,isAuthenticated:t.isAuthenticated,token:t.token,login:r,register:o,logout:i,updateProfile:c,error:t.error,refreshCustomer:l};return s.jsx(n.Provider,{value:d,children:e})}},30292:(e,t,r)=>{"use strict";r.d(t,{Gd:()=>i,default:()=>l});var s=r(10326),o=r(17577),a=r(60114),n=r(85251);let i=(0,a.Ue)()((0,n.tJ)(e=>({isLaunchingSoon:!1,setIsLaunchingSoon:t=>{e({isLaunchingSoon:t})}}),{name:"ankkor-launch-state",skipHydration:!0,storage:{getItem:e=>null,setItem:(e,t)=>{},removeItem:e=>{}}})),c=(0,o.createContext)(void 0),l=({children:e})=>{let t=i(),[r,a]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{i.persist.rehydrate(),a(!0)},[]),s.jsx(c.Provider,{value:t,children:e})}},2861:(e,t,r)=>{"use strict";r.d(t,{default:()=>x,r:()=>u});var s=r(10326),o=r(17577),a=r(35047),n=r(86462),i=r(92148);let c=({size:e="md",variant:t="thread",className:r=""})=>{let o={sm:{container:"w-16 h-16",text:"text-xs"},md:{container:"w-24 h-24",text:"text-sm"},lg:{container:"w-32 h-32",text:"text-base"}};return"thread"===t?(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[(0,s.jsxs)("div",{className:`relative ${o[e].container}`,children:[s.jsx(i.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27",borderRightColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1.5,repeat:1/0,ease:"linear"}}),s.jsx(i.E.div,{className:"absolute inset-2 rounded-full border-2 border-[#e5e2d9]",style:{borderBottomColor:"#8a8778",borderLeftColor:"#8a8778"},animate:{rotate:-360},transition:{duration:2,repeat:1/0,ease:"linear"}}),s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx("div",{className:"w-2 h-2 rounded-full bg-[#2c2c27]"})})]}),s.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Loading Collection"})]}):"fabric"===t?(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[(0,s.jsxs)("div",{className:`relative ${o[e].container} flex items-center justify-center`,children:[s.jsx(i.E.div,{className:"absolute w-1/3 h-1/3 bg-[#e5e2d9]",animate:{rotate:360,scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),s.jsx(i.E.div,{className:"absolute w-1/3 h-1/3 bg-[#8a8778]",animate:{rotate:-360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.3}}),s.jsx(i.E.div,{className:"absolute w-1/3 h-1/3 bg-[#2c2c27]",animate:{rotate:360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.6}})]}),s.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Preparing Your Style"})]}):"button"===t?(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[s.jsx("div",{className:`relative ${o[e].container} flex items-center justify-center`,children:s.jsx("div",{className:"relative flex",children:[0,1,2,3].map(e=>s.jsx(i.E.div,{className:"w-3 h-3 mx-1 rounded-full bg-[#2c2c27] border border-[#8a8778]",animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,ease:"easeInOut",delay:.2*e}},e))})}),s.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Tailoring Experience"})]}):(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[s.jsx("div",{className:`relative ${o[e].container}`,children:s.jsx(i.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),s.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Loading"})]})},l=({isLoading:e,variant:t="thread"})=>s.jsx(n.M,{children:e&&s.jsx(i.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[200] flex items-center justify-center bg-[#f8f8f5]/90 backdrop-blur-sm",children:s.jsx(c,{variant:t,size:"lg"})})}),d=(0,o.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),u=()=>(0,o.useContext)(d),h={"/collection":"fabric","/collection/shirts":"fabric","/collection/polos":"fabric","/product":"thread","/about":"button","/customer-service":"button","/account":"thread","/wishlist":"thread"},m=({setIsLoading:e,setVariant:t})=>{let r;let s=(0,a.usePathname)();try{r=(0,a.useSearchParams)()}catch(e){r=null}return(0,o.useEffect)(()=>{e(!0),t(h["/"+s.split("/")[1]]||h[s]||"thread");let r=setTimeout(()=>{e(!1)},1200);return()=>clearTimeout(r)},[s,r,e,t]),null},g=()=>s.jsx("div",{className:"hidden",children:"Loading route..."}),x=({children:e})=>{let[t,r]=(0,o.useState)(!1),[a,n]=(0,o.useState)("thread");return(0,s.jsxs)(d.Provider,{value:{isLoading:t,setLoading:r,variant:a,setVariant:n},children:[s.jsx(o.Suspense,{fallback:s.jsx(g,{}),children:s.jsx(m,{setIsLoading:r,setVariant:n})}),e,s.jsx(l,{isLoading:t,variant:a})]})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>m});var s=r(10326),o=r(17577),a=r(92148),n=r(86462),i=r(54659),c=r(87888),l=r(18019),d=r(94019),u=r(91459);let h=(0,o.createContext)(void 0);function m({children:e}){let[t,r]=(0,o.useState)([]),a=(e,t="info",s=3e3)=>{let o=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:o,message:e,type:t,duration:s}])},n=e=>{r(t=>t.filter(t=>t.id!==e))};return(0,u.OR)("notification:show",({message:e,type:t,duration:r})=>{a(e,t,r)}),(0,u.OR)("notification:hide",({id:e})=>{n(e)}),(0,s.jsxs)(h.Provider,{value:{toasts:t,addToast:a,removeToast:n},children:[e,s.jsx(x,{})]})}function g({toast:e,onRemove:t}){return(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(i.Z,{className:"h-5 w-5"});case"error":return s.jsx(c.Z,{className:"h-5 w-5"});default:return s.jsx(l.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(d.Z,{className:"h-4 w-4"})})]})}function x(){let{toasts:e,removeToast:t}=function(){let e=(0,o.useContext)(h);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(n.M,{children:e.map(e=>s.jsx(g,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},32273:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(17577);let o=()=>{},a=()=>((0,s.useEffect)(()=>{o()},[]),null)},94001:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i,a:()=>c});var s=r(10326),o=r(17577),a=r(91459);let n=(0,o.createContext)(void 0);function i({children:e}){let[t,r]=(0,o.useState)(null),[i,c]=(0,o.useState)(null),[l,d]=(0,o.useState)(!0),[u,h]=(0,o.useState)(null),m=async(e,t)=>{d(!0),h(null);try{let s=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"}),o=await s.json();if(o.success)r(o.user),c(o.token||"authenticated"),a.iK.loginSuccess(o.user,o.token||"authenticated"),a.Cc.show("Login successful!","success");else{let e=o.message||"Login failed";throw h(e),a.iK.loginError(e),a.Cc.show(e,"error"),Error(e)}}catch(t){let e=t.message||"Login failed";throw h(e),a.iK.loginError(e),a.Cc.show(e,"error"),t}finally{d(!1)}},g=async e=>{d(!0),h(null);try{let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",...e}),credentials:"include"}),s=await t.json();if(s.success)r(s.user),c(s.token||"authenticated"),a.iK.registerSuccess(s.user,s.token||"authenticated"),a.Cc.show("Registration successful!","success");else{let e=s.message||"Registration failed";throw h(e),a.iK.registerError(e),a.Cc.show(e,"error"),Error(e)}}catch(t){let e=t.message||"Registration failed";throw h(e),a.iK.registerError(e),a.Cc.show(e,"error"),t}finally{d(!1)}},x=async()=>{d(!0);try{await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"logout"}),credentials:"include"})}catch(e){console.error("Logout API call failed:",e)}r(null),c(null),h(null),a.iK.logout(),a.Cc.show("Logged out successfully","info"),d(!1)},f=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&t.user?(r(t.user),c(t.token||"authenticated")):(r(null),c(null))}else r(null),c(null)}catch(e){console.error("Failed to refresh session:",e),r(null),c(null)}},p=async e=>{d(!0),h(null);try{let t=await fetch("/api/auth/update-profile",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),credentials:"include"}),s=await t.json();if(s.success)return r(s.user),a.iK.profileUpdated(s.user),a.Cc.show("Profile updated successfully!","success"),s.user;{let e=s.message||"Profile update failed";throw h(e),a.Cc.show(e,"error"),Error(e)}}catch(t){let e=t.message||"Profile update failed";throw h(e),a.Cc.show(e,"error"),t}finally{d(!1)}};return s.jsx(n.Provider,{value:{user:t,token:i,isAuthenticated:!!t&&!!i,isLoading:l,error:u,login:m,register:g,logout:x,refreshSession:f,updateProfile:p,clearError:()=>{h(null)}},children:e})}function c(){let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},91459:(e,t,r)=>{"use strict";r.d(t,{Cc:()=>n,OR:()=>i,iK:()=>a}),r(17577);class s{on(e,t){return this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t),()=>{this.off(e,t)}}off(e,t){let r=this.listeners.get(e);r&&(r.delete(t),0===r.size&&this.listeners.delete(e))}emit(e,t){let r=this.listeners.get(e);r&&r.forEach(r=>{try{r(t)}catch(t){console.error(`Error in event listener for ${e}:`,t)}})}once(e,t){let r=s=>{t(s),this.off(e,r)};this.on(e,r)}removeAllListeners(e){e?this.listeners.delete(e):this.listeners.clear()}listenerCount(e){return this.listeners.get(e)?.size||0}eventNames(){return Array.from(this.listeners.keys())}constructor(){this.listeners=new Map}}let o=new s,a={loginSuccess:(e,t)=>o.emit("auth:login-success",{user:e,token:t}),loginError:e=>o.emit("auth:login-error",{error:e}),logout:()=>o.emit("auth:logout",void 0),registerSuccess:(e,t)=>o.emit("auth:register-success",{user:e,token:t}),registerError:e=>o.emit("auth:register-error",{error:e}),profileUpdated:e=>o.emit("auth:profile-updated",{user:e}),sessionExpired:()=>o.emit("auth:session-expired",void 0)},n={show:(e,t="info",r)=>o.emit("notification:show",{message:e,type:t,duration:r}),hide:e=>o.emit("notification:hide",{id:e})};function i(e,t,r=[]){}},86806:(e,t,r)=>{"use strict";r.d(t,{rY:()=>i});var s=r(60114),o=r(85251);let a=()=>Math.random().toString(36).substring(2,15),n=async(e,t,r)=>{try{let s=await fetch(`/api/products/${e}/stock${r?`?variation_id=${r}`:""}`);if(!s.ok)return console.warn("Stock validation API failed, allowing add to cart"),{available:!0,message:"Stock validation temporarily unavailable"};let o=await s.json();if("IN_STOCK"!==o.stockStatus&&"instock"!==o.stockStatus)return{available:!1,message:"This product is currently out of stock",stockStatus:o.stockStatus};if(null!==o.stockQuantity&&o.stockQuantity<t)return{available:!1,message:`Only ${o.stockQuantity} items available in stock`,stockQuantity:o.stockQuantity,stockStatus:o.stockStatus};return{available:!0,stockQuantity:o.stockQuantity,stockStatus:o.stockStatus}}catch(e){return console.error("Stock validation error:",e),console.warn("Stock validation failed, allowing add to cart for better UX"),{available:!0,message:"Stock validation temporarily unavailable"}}},i=(0,s.Ue)()((0,o.tJ)((e,t)=>({items:[],itemCount:0,isLoading:!1,error:null,addToCart:async r=>{e({isLoading:!0,error:null});try{let s,o;let i=await n(r.productId,r.quantity,r.variationId);if(!i.available)throw Error(i.message||"Product is out of stock");try{let e=await fetch("/api/reservations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"create",productId:r.productId,quantity:r.quantity,userId:`cart_user_${Date.now()}`,variationId:r.variationId})}),t=await e.json();t.success&&t.reservation?(s=t.reservation.id,o=t.reservation.expiresAt,console.log(`Stock reserved for ${r.name}: ${s} (expires: ${o})`)):console.warn("Failed to create stock reservation:",t.error)}catch(e){console.warn("Stock reservation failed, continuing without reservation:",e)}let c=t().items,l=r.price;"string"==typeof l&&(l=l.replace(/[₹$€£]/g,"").trim().replace(/,/g,""));let d={...r,price:l},u=c.findIndex(e=>e.productId===d.productId&&e.variationId===d.variationId);if(-1!==u){let t=[...c];t[u].quantity+=d.quantity,s&&(t[u].reservationId=s,t[u].reservedUntil=o),e({items:t,itemCount:t.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}else{let t={...d,id:a(),reservationId:s,reservedUntil:o};e({items:[...c,t],itemCount:c.reduce((e,t)=>e+t.quantity,0)+t.quantity,isLoading:!1})}console.log("Item added to cart successfully")}catch(t){console.error("Error adding item to cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:(r,s)=>{e({isLoading:!0,error:null});try{let o=t().items;if(s<=0)return t().removeCartItem(r);let a=o.map(e=>e.id===r?{...e,quantity:s}:e);e({items:a,itemCount:a.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}catch(t){console.error("Error updating cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:r=>{e({isLoading:!0,error:null});try{let s=t().items.filter(e=>e.id!==r);e({items:s,itemCount:s.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}catch(t){console.error("Error removing cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},clearCart:()=>{e({items:[],itemCount:0,isLoading:!1,error:null})},setError:t=>{e({error:t})},setIsLoading:t=>{e({isLoading:t})},subtotal:()=>{let e=t().items;try{let t=e.reduce((e,t)=>{let r=0;if("string"==typeof t.price){let e=t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");r=parseFloat(e)}else r=t.price;return isNaN(r)?(console.warn(`Invalid price for item ${t.id}: ${t.price}`),e):e+r*t.quantity},0);return isNaN(t)?0:t}catch(e){return console.error("Error calculating subtotal:",e),0}},total:()=>{let e=t().subtotal();return isNaN(e)?0:e},syncWithWooCommerce:async r=>{let{items:s}=t();if(0===s.length)throw Error("Cart is empty");try{if(console.log("Syncing cart with WooCommerce..."),console.log("Auth token provided:",!!r),e({isLoading:!0}),r){console.log("User is authenticated, using JWT-to-Cookie bridge");try{let t=await c(r,s);return e({isLoading:!1}),t}catch(e){console.error("JWT-to-Cookie bridge failed:",e),console.log("Falling back to guest checkout...")}}console.log("User is not authenticated, redirecting to WooCommerce checkout");let t="https://maroon-lapwing-781450.hostingersite.com/checkout/";return console.log("Guest checkout URL:",t),e({isLoading:!1}),t}catch(t){console.error("Error syncing cart with WooCommerce:",t),e({isLoading:!1});try{console.log("Attempting fallback method for cart sync...");let e="https://maroon-lapwing-781450.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1";return s.forEach((t,r)=>{0===r?e+=`&add-to-cart=${t.productId}&quantity=${t.quantity}`:e+=`&add-to-cart[${r}]=${t.productId}&quantity[${r}]=${t.quantity}`,t.variationId&&(e+=`&variation_id=${t.variationId}`)}),console.log("Fallback checkout URL:",e),e}catch(e){throw console.error("Fallback method failed:",e),Error("Failed to sync cart with WooCommerce. Please try again or contact support.")}}}}),{name:"ankkor-local-cart",version:1,skipHydration:!0}));async function c(e,t){if(!e)throw Error("Authentication token is required");let r="https://maroon-lapwing-781450.hostingersite.com",s="https://maroon-lapwing-781450.hostingersite.com/checkout/";if(!r||!s)throw Error("WordPress or checkout URL not configured. Check your environment variables.");try{console.log("Creating WordPress session from JWT token..."),console.log("Using endpoint:",`${r}/wp-json/headless/v1/create-wp-session`),console.log("Token length:",e.length),console.log("Token preview:",e.substring(0,20)+"...");let t=await fetch(`${r}/wp-json/headless/v1/create-wp-session`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({token:e}),credentials:"include"});if(console.log("Response status:",t.status),console.log("Response headers:",Object.fromEntries(t.headers.entries())),!t.ok){let e=`HTTP ${t.status}: ${t.statusText}`;try{let r=await t.json();e=r.message||r.code||e,console.error("Error response data:",r)}catch(e){console.error("Could not parse error response:",e)}throw Error(`Failed to create WordPress session: ${e}`)}let o=await t.json();if(console.log("Response data:",o),!o.success)throw Error(o.message||"Failed to create WordPress session");return console.log("WordPress session created successfully"),console.log("Redirecting to checkout URL:",s),s}catch(e){if(console.error("Error creating WordPress session:",e),e instanceof TypeError&&e.message.includes("fetch"))throw Error("Network error: Could not connect to WordPress. Please check your internet connection.");throw Error(e instanceof Error?e.message:"Failed to prepare checkout")}}},7629:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\error.tsx#default`)},43315:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\global-error.tsx#default`)},11360:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j,metadata:()=>w});var s=r(19510),o=r(10527),a=r.n(o),n=r(36822),i=r.n(n);r(5023);var c=r(68570);(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#useCart`),(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#CartProvider`);let l=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#default`);(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#useLoading`),(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#LoadingProvider`);let d=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#default`);(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let u=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),h=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\contexts\AuthContext.tsx#AuthProvider`);(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\contexts\AuthContext.tsx#useAuth`);let m=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`),(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#useLaunchingSoonStore`),(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#useLaunchingSoon`),(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#LaunchingSoonProvider`);let g=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#default`),x=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\LaunchingStateInitializer.tsx#default`),f=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\utils\LaunchUtilsInitializer.tsx#default`),p=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\StoreHydrationInitializer.tsx#default`),v=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartWrapper.tsx#default`),y=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\layout\NavbarWrapperSSR.tsx#default`),k=(0,c.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\layout\FooterWrapperSSR.tsx#default`),w={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function j({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${a().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(m,{children:s.jsx(h,{children:s.jsx(u,{children:(0,s.jsxs)(l,{children:[s.jsx(d,{children:(0,s.jsxs)(g,{children:[s.jsx(x,{}),s.jsx(f,{}),s.jsx(p,{}),s.jsx(y,{}),s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e}),s.jsx(k,{})]})}),s.jsx(v,{})]})})})})})})}},11930:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(19510),o=r(9811);function a(){return s.jsx("div",{className:"min-h-screen bg-[#f8f8f5] flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx(o.Z,{className:"w-8 h-8 text-[#2c2c27] animate-spin"})}),s.jsx("p",{className:"text-[#2c2c27] font-medium",children:"Loading..."})]})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};