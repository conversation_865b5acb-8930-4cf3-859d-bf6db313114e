(()=>{var e={};e.id=4456,e.ids=[4456],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},51118:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.Z,__next_app__:()=>m,originalPathname:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l}),r(99106),r(11360),r(7629),r(11930),r(12523);var s=r(23191),a=r(88716),i=r(43315),n=r(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let l=["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99106)),"E:\\ankkorwoo\\ankkor\\src\\app\\wishlist\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\wishlist\\page.tsx"],c="/wishlist/page",m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94373:(e,t,r)=>{Promise.resolve().then(r.bind(r,79626))},32933:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},98091:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(69029),a=r.n(s)},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let s=r(91174),a=r(23078),i=r(92481),n=s._(r(86820));function o(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=i.Image},79626:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>j});var a=r(10326),i=r(17577),n=r(90434),o=r(46226),l=r(67427),d=r(94019),c=r(98091),m=r(34565),u=r(32933),h=r(96040),x=r(68897),f=r(68211),p=r(92148),v=r(40381),g=r(91664),y=e([h]);h=(y.then?(await y)():y)[0];let b=e=>{if("number"==typeof e)return e;if(!e)return 0;let t=e.toString().replace(/[^\d.-]/g,""),r=parseFloat(t);return isNaN(r)?0:r},w=e=>b(e).toFixed(2);function j(){let e=(0,h.x)(),{items:t,removeFromWishlist:r,clearWishlist:s}=(0,h.Y)(),{isAuthenticated:y,isLoading:j}=(0,x.O)(),[b,N]=(0,i.useState)(!0),[k,P]=(0,i.useState)({}),[C,I]=(0,i.useState)(!1),_=(t,s=!1)=>{try{if(!t.variantId||"string"!=typeof t.variantId||""===t.variantId.trim()){console.error("Invalid variant ID:",t.variantId),v.Am.error("Unable to add this item to your cart. Invalid product variant.");return}let a=t.variantId;if(!a.startsWith("gid://"))try{let e=a.replace(/\D/g,"");if(!e)throw Error(`Could not extract a valid numeric ID from "${a}"`);a=`gid://shopify/ProductVariant/${e}`}catch(e){console.error("Failed to format variant ID:",e),v.Am.error("This product has an invalid variant ID format.");return}console.log(`Adding item to cart: ${t.name||"Unnamed Product"} with variant ID: ${a}`),e.addItem({productId:t.id,variantId:a,title:t.name||"Unnamed Product",handle:t.handle||"#",image:t.image||"/placeholder-image.jpg",price:t.price?w(t.price):"0.00",quantity:1,currencyCode:"INR"}).then(()=>{v.Am.success(`${t.name||"Product"} added to your cart!`),P(e=>({...e,[t.id]:!0})),setTimeout(()=>{P(e=>({...e,[t.id]:!1}))},2e3),s&&r(t.id)}).catch(e=>{console.error("Error from cart.addItem:",e),e.message?.includes("variant is no longer available")?v.Am.error("This product is no longer available in the store."):e.message?.includes("Invalid variant ID")?v.Am.error("This product has an invalid variant format. Please try another item."):v.Am.error("Unable to add this item to your cart. Please try again later.")})}catch(e){console.error("Error in handleAddToCart:",e),v.Am.error("An unexpected error occurred. Please try again later.")}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[a.jsx("h1",{className:"text-3xl font-serif",children:"My Wishlist"}),t.length>0&&a.jsx(g.z,{variant:"outline",onClick:s,className:"text-sm",children:"Clear All"})]}),b?a.jsx("div",{className:"flex items-center justify-center py-24",children:a.jsx(f.Z,{size:"lg",color:"#8a8778"})}):(0,a.jsxs)(a.Fragment,{children:[!y&&t.length>0&&!C&&a.jsx("div",{className:"mb-6 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(l.Z,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:["Your wishlist is saved locally on this device.",a.jsx(n.default,{href:"/sign-up",className:"ml-1 font-medium underline hover:no-underline",children:"Create an account"})," to access it from anywhere."]})]})}),!y&&C&&t.length>0&&a.jsx(p.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8 p-4 border border-[#e5e2d9] bg-[#f8f8f5] rounded-md",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx(l.Z,{className:"h-5 w-5 text-[#8a8778] mt-1 mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-serif font-medium text-[#2c2c27]",children:"Sync your wishlist across devices"}),a.jsx("p",{className:"text-sm text-[#5c5c52] mt-1",children:"Your wishlist works without an account and is saved locally. Sign in to sync it across all your devices."})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(n.default,{href:"/sign-up",className:"text-sm text-[#2c2c27] font-medium hover:text-[#8a8778] transition-colors",children:"Sign Up"}),a.jsx("button",{onClick:()=>{I(!1)},className:"text-[#8a8778] hover:text-[#2c2c27] transition-colors","aria-label":"Dismiss",children:a.jsx(d.Z,{className:"h-4 w-4"})})]})]})}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-16",children:[a.jsx("div",{className:"inline-flex justify-center items-center w-16 h-16 bg-gray-100 rounded-full mb-4",children:a.jsx(l.Z,{className:"h-8 w-8 text-gray-400"})}),a.jsx("h2",{className:"text-xl font-medium mb-2",children:"Your wishlist is empty"}),a.jsx("p",{className:"text-gray-500 mb-2",children:"Add items you love to your wishlist. Review them anytime and easily move them to the cart."}),!y&&a.jsx("p",{className:"text-sm text-gray-400 mb-6",children:"No account needed - your wishlist is saved locally on this device."}),a.jsx(n.default,{href:"/categories",children:a.jsx(g.z,{children:"Continue Shopping"})})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,a.jsxs)("div",{className:"border rounded-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(n.default,{href:`/product/${e.handle||"#"}`,children:a.jsx("div",{className:"aspect-square relative bg-gray-100",children:a.jsx(o.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover"})})}),a.jsx("button",{onClick:()=>r(e.id),className:"absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100","aria-label":"Remove from wishlist",children:a.jsx(c.Z,{className:"h-4 w-4 text-gray-600"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[a.jsx(n.default,{href:`/product/${e.handle||"#"}`,children:a.jsx("h2",{className:"font-medium text-lg hover:underline",children:e.name||"Unnamed Product"})}),(0,a.jsxs)("p",{className:"text-gray-700 my-2",children:["₹",e.price?w(e.price):"0.00"]}),(0,a.jsxs)(g.z,{onClick:()=>_(e),className:"w-full mt-2 flex items-center justify-center gap-2",children:[a.jsx(m.Z,{className:"h-4 w-4"}),"Add to Cart"]})]})]},e.id))}),a.jsx("div",{className:"mt-12 text-center",children:a.jsx(n.default,{href:"/categories",children:a.jsx(g.z,{variant:"outline",children:"Continue Shopping"})})}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse",children:[a.jsx("thead",{className:"border-b border-[#e5e2d9]",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Product"}),a.jsx("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Price"}),a.jsx("th",{className:"py-4 text-center font-serif text-[#2c2c27]",children:"Actions"})]})}),a.jsx("tbody",{className:"divide-y divide-[#e5e2d9]",children:t.map(e=>(0,a.jsxs)("tr",{className:"group",children:[a.jsx("td",{className:"py-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"relative mr-4 h-24 w-20 overflow-hidden bg-[#f4f3f0]",children:a.jsx(n.default,{href:`/product/${e.handle||"#"}`,children:a.jsx(o.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 80px, 120px",className:"object-cover object-center transition-transform duration-500 group-hover:scale-105"})})}),(0,a.jsxs)("div",{children:[a.jsx(n.default,{href:`/product/${e.handle||"#"}`,className:"font-serif text-lg text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:e.name||"Unnamed Product"}),a.jsx("p",{className:"text-sm text-[#8a8778]",children:e.material||"Material not specified"})]})]})}),(0,a.jsxs)("td",{className:"py-6 font-medium text-[#2c2c27]",children:["₹",e.price?w(e.price):"0.00"]}),a.jsx("td",{className:"py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[a.jsx(p.E.button,{onClick:()=>_(e),className:`${k[e.id]?"bg-[#2c2c27] text-[#f4f3f0]":"text-[#2c2c27]"} p-2 rounded-full transition-colors hover:text-[#8a8778]`,"aria-label":"Add to cart",whileTap:{scale:.95},children:k[e.id]?a.jsx(u.Z,{className:"h-5 w-5"}):a.jsx(m.Z,{className:"h-5 w-5"})}),a.jsx(p.E.button,{onClick:()=>r(e.id),className:"text-[#2c2c27] p-2 rounded-full hover:text-[#8a8778] transition-colors","aria-label":"Remove from wishlist",whileTap:{scale:.95},children:a.jsx(d.Z,{className:"h-5 w-5"})})]})})]},e.id))})]})})]})]}),a.jsx(v.x7,{position:"top-center",toastOptions:{duration:3e3,style:{background:"#F8F8F5",color:"#2C2C27",border:"1px solid #E5E2D9"},success:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}},error:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}}}})]})}s()}catch(e){s(e)}})},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var s=r(10326);r(17577);var a=r(34214),i=r(79360),n=r(51223);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:i=!1,...l}){let d=i?a.g7:"button";return s.jsx(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...l})}},68211:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(10326);r(17577);let a=({size:e="md",color:t="#2c2c27",className:r=""})=>{let a={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,s.jsxs)(s.Fragment,{children:[s.jsx("style",{children:`
        @keyframes loaderRotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        
        @keyframes loaderDot1 {
          0%, 100% {
            opacity: 0.2;
          }
          25% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot2 {
          0%, 100% {
            opacity: 0.2;
          }
          50% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot3 {
          0%, 100% {
            opacity: 0.2;
          }
          75% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot4 {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.2;
          }
        }
      `}),s.jsx("div",{className:`flex items-center justify-center ${r}`,children:(0,s.jsxs)("div",{className:`relative ${a[e].container}`,children:[s.jsx("div",{className:`absolute top-0 left-1/2 -translate-x-1/2 ${a[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot1 1.5s infinite"}}),s.jsx("div",{className:`absolute top-1/2 right-0 -translate-y-1/2 ${a[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot2 1.5s infinite"}}),s.jsx("div",{className:`absolute bottom-0 left-1/2 -translate-x-1/2 ${a[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot3 1.5s infinite"}}),s.jsx("div",{className:`absolute top-1/2 left-0 -translate-y-1/2 ${a[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot4 1.5s infinite"}}),s.jsx("div",{className:"absolute inset-0 rounded-full",style:{border:`2px solid ${t}`,borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"}})]})})]})}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(41135),a=r(31009);function i(...e){return(0,a.m6)((0,s.W)(e))}},99106:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\wishlist\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1057,2481,8578,2325,5436,5725,5916],()=>r(51118));module.exports=s})();