{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "0MfyuW5bDwJlD3iAVhRdX", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "rE+5h9tuRpE3Zfr6WYIQ/SBvNCK/zDIpEBE7lBTWPrg=", "__NEXT_PREVIEW_MODE_ID": "1b4d5af2da1921ecda5254f9ad5eadd7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e7eb2e430bc45b7742b7283cb70ebb5b8dc6a8b493d5451c869f5797541d6c93", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "151356682509367ee4c29c81a9c9fb4b7f7b502173eeaa06bd698c7dcc1404fb"}}}, "functions": {}, "sortedMiddleware": ["/"]}