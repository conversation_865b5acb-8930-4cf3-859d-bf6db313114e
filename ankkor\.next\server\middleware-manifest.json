{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "BQm41cz3mW7dTINH1C9UJ", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WEHE8b1RtHPQnayi8/yYAjRUda8br09Hvbn6JDGcOAE=", "__NEXT_PREVIEW_MODE_ID": "5b4c013394f327ca55bc77bc128d1dd3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "929abd91f39b102b7e05c1fdee4f967c3099c1c3c130e85487e5a5721ec9512b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d1443d16a81bc6af5e7d684542515764d12bd36fb7fd8005354fa4cb84533036"}}}, "functions": {}, "sortedMiddleware": ["/"]}