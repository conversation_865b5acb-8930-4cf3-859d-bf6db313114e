{"/_not-found/page": "app/_not-found/page.js", "/about/page": "app/about/page.js", "/about/craftsmanship/page": "app/about/craftsmanship/page.js", "/api/admin/migrate-inventory-mappings/route": "app/api/admin/migrate-inventory-mappings/route.js", "/about/sustainability/page": "app/about/sustainability/page.js", "/api/auth/route": "app/api/auth/route.js", "/admin/products/page": "app/admin/products/page.js", "/api/checkout/route": "app/api/checkout/route.js", "/api/graphql/route": "app/api/graphql/route.js", "/api/auth/update-profile/route": "app/api/auth/update-profile/route.js", "/api/products/[id]/stock/route": "app/api/products/[id]/stock/route.js", "/api/cache/products/[handle]/route": "app/api/cache/products/[handle]/route.js", "/api/products/validate-stock/route": "app/api/products/validate-stock/route.js", "/api/razorpay/verify-payment/route": "app/api/razorpay/verify-payment/route.js", "/api/razorpay/test-order/route": "app/api/razorpay/test-order/route.js", "/api/razorpay/create-order/route": "app/api/razorpay/create-order/route.js", "/api/reconcile/route": "app/api/reconcile/route.js", "/api/reservations/cleanup/route": "app/api/reservations/cleanup/route.js", "/api/reservations/route": "app/api/reservations/route.js", "/api/razorpay/test-connection/route": "app/api/razorpay/test-connection/route.js", "/api/shipping-rates/route": "app/api/shipping-rates/route.js", "/api/revalidate/route": "app/api/revalidate/route.js", "/api/stock-updates/route": "app/api/stock-updates/route.js", "/api/user/wishlist/route": "app/api/user/wishlist/route.js", "/api/trigger-test/route": "app/api/trigger-test/route.js", "/api/test/route": "app/api/test/route.js", "/api/webhooks/route": "app/api/webhooks/route.js", "/api/webhooks/order/route": "app/api/webhooks/order/route.js", "/api/webhooks/test/route": "app/api/webhooks/test/route.js", "/cart-test/page": "app/cart-test/page.js", "/api/webhooks/inventory/route": "app/api/webhooks/inventory/route.js", "/checkout/page": "app/checkout/page.js", "/category/[slug]/page": "app/category/[slug]/page.js", "/customer-service/contact/page": "app/customer-service/contact/page.js", "/collection/page": "app/collection/page.js", "/collection/polos/page": "app/collection/polos/page.js", "/customer-service/page": "app/customer-service/page.js", "/api/webhooks/simple/route": "app/api/webhooks/simple/route.js", "/local-cart-test/page": "app/local-cart-test/page.js", "/collection/shirts/page": "app/collection/shirts/page.js", "/customer-service/size-guide/page": "app/customer-service/size-guide/page.js", "/order-confirmed/page": "app/order-confirmed/page.js", "/page": "app/page.js", "/product/[slug]/page": "app/product/[slug]/page.js", "/customer-service/faq/page": "app/customer-service/faq/page.js", "/return-policy/page": "app/return-policy/page.js", "/robots.txt/route": "app/robots.txt/route.js", "/shipping-policy/page": "app/shipping-policy/page.js", "/search/page": "app/search/page.js", "/privacy-policy/page": "app/privacy-policy/page.js", "/terms-of-service/page": "app/terms-of-service/page.js", "/test-auth/page": "app/test-auth/page.js", "/test-woo/page": "app/test-woo/page.js", "/test-auth/success/page": "app/test-auth/success/page.js", "/test/page": "app/test/page.js", "/wishlist/page": "app/wishlist/page.js", "/woocommerce-cart-test/page": "app/woocommerce-cart-test/page.js", "/woocommerce-test/success/page": "app/woocommerce-test/success/page.js", "/woocommerce-test/page": "app/woocommerce-test/page.js", "/woocommerce-checkout-test/page": "app/woocommerce-checkout-test/page.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/auth/user/route": "app/api/auth/user/route.js", "/api/ankkor/v1/nonce/route": "app/api/ankkor/v1/nonce/route.js", "/account/page": "app/account/page.js", "/api/cron/inventory-sync/route": "app/api/cron/inventory-sync/route.js", "/api/products/route": "app/api/products/route.js", "/api/debug/route": "app/api/debug/route.js", "/api/nonce/route": "app/api/nonce/route.js", "/categories/page": "app/categories/page.js", "/api/woo-sync/route": "app/api/woo-sync/route.js", "/sign-up/page": "app/sign-up/page.js", "/sign-in/page": "app/sign-in/page.js"}