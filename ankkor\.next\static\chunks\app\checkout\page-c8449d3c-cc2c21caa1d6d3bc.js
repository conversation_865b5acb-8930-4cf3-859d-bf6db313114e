(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6582],{46714:function(a,r,i){"use strict";i.d(r,{p:function(){return o}});var e=i(59625),n=i(89134),t=i(67654);let o=(0,e.Ue)()((0,n.tJ)((a,r)=>({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null,setCart:i=>{let e=i.reduce((a,r)=>a+("string"==typeof r.price?parseFloat(r.price):r.price)*r.quantity,0),{shippingCost:n}=r();a({cart:i,subtotal:e,finalAmount:e+n})},setShippingAddress:r=>{a({shippingAddress:r})},fetchShippingRates:async(i,e)=>{let{cart:n,subtotal:o}=r();if(!i||i.length<6){a({error:"Please enter a valid pincode"});return}a({isLoadingShipping:!0,error:null});try{let r=await (0,t.Mq)(i,n,e),s=r.length>0?r[0]:null,h=s?s.cost:0;a({shippingOptions:r,isLoadingShipping:!1,selectedShipping:s,shippingCost:h,finalAmount:o+h})}catch(r){console.error("Error fetching shipping rates:",r),a({error:r instanceof Error?r.message:"Failed to fetch shipping rates",isLoadingShipping:!1,shippingOptions:[]})}},setSelectedShipping:i=>{let{subtotal:e}=r(),n=e+i.cost;a({selectedShipping:i,shippingCost:i.cost,finalAmount:n})},calculateFinalAmount:()=>{let{subtotal:i,shippingCost:e,finalAmount:n}=r(),t=i+e;t!==n&&a({finalAmount:t})},setError:r=>{a({error:r})},setProcessingPayment:r=>{a({isProcessingPayment:r})},clearCheckout:()=>{a({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null})}}),{name:"checkout-storage",partialize:a=>({shippingAddress:a.shippingAddress,selectedShipping:a.selectedShipping})}))},4139:function(a,r,i){"use strict";i.d(r,{Rh:function(){return t},nY:function(){return o},v8:function(){return n}});let e=[{name:"Punjab",code:"PB",cities:["Amritsar","Ludhiana","Jalandhar","Patiala","Bathinda","Mohali","Pathankot","Moga","Abohar","Malerkotla"]},{name:"Delhi",code:"DL",cities:["New Delhi","Delhi","North Delhi","South Delhi","East Delhi","West Delhi","Central Delhi"]},{name:"Maharashtra",code:"MH",cities:["Mumbai","Pune","Nagpur","Nashik","Aurangabad","Solapur","Amravati","Kolhapur","Sangli","Malegaon"]},{name:"Karnataka",code:"KA",cities:["Bangalore","Mysore","Hubli","Mangalore","Belgaum","Gulbarga","Davanagere","Bellary","Bijapur","Shimoga"]},{name:"Tamil Nadu",code:"TN",cities:["Chennai","Coimbatore","Madurai","Tiruchirappalli","Salem","Tirunelveli","Tiruppur","Vellore","Erode","Thoothukudi"]},{name:"Gujarat",code:"GJ",cities:["Ahmedabad","Surat","Vadodara","Rajkot","Bhavnagar","Jamnagar","Junagadh","Gandhinagar","Anand","Navsari"]},{name:"Rajasthan",code:"RJ",cities:["Jaipur","Jodhpur","Udaipur","Kota","Bikaner","Ajmer","Bhilwara","Alwar","Bharatpur","Sikar"]},{name:"West Bengal",code:"WB",cities:["Kolkata","Howrah","Durgapur","Asansol","Siliguri","Malda","Bardhaman","Baharampur","Habra","Kharagpur"]},{name:"Uttar Pradesh",code:"UP",cities:["Lucknow","Kanpur","Ghaziabad","Agra","Varanasi","Meerut","Allahabad","Bareilly","Aligarh","Moradabad"]},{name:"Haryana",code:"HR",cities:["Gurgaon","Faridabad","Panipat","Ambala","Yamunanagar","Rohtak","Hisar","Karnal","Sonipat","Panchkula"]},{name:"Madhya Pradesh",code:"MP",cities:["Bhopal","Indore","Gwalior","Jabalpur","Ujjain","Sagar","Dewas","Satna","Ratlam","Rewa"]},{name:"Bihar",code:"BR",cities:["Patna","Gaya","Bhagalpur","Muzaffarpur","Purnia","Darbhanga","Bihar Sharif","Arrah","Begusarai","Katihar"]},{name:"Odisha",code:"OR",cities:["Bhubaneswar","Cuttack","Rourkela","Brahmapur","Sambalpur","Puri","Balasore","Bhadrak","Baripada","Jharsuguda"]},{name:"Kerala",code:"KL",cities:["Thiruvananthapuram","Kochi","Kozhikode","Thrissur","Kollam","Palakkad","Alappuzha","Malappuram","Kannur","Kasaragod"]},{name:"Jharkhand",code:"JH",cities:["Ranchi","Jamshedpur","Dhanbad","Bokaro","Deoghar","Phusro","Hazaribagh","Giridih","Ramgarh","Medininagar"]},{name:"Assam",code:"AS",cities:["Guwahati","Silchar","Dibrugarh","Jorhat","Nagaon","Tinsukia","Tezpur","Bongaigaon","Dhubri","North Lakhimpur"]},{name:"Chhattisgarh",code:"CG",cities:["Raipur","Bhilai","Bilaspur","Korba","Durg","Rajnandgaon","Jagdalpur","Raigarh","Ambikapur","Mahasamund"]},{name:"Uttarakhand",code:"UK",cities:["Dehradun","Haridwar","Roorkee","Haldwani","Rudrapur","Kashipur","Rishikesh","Kotdwar","Pithoragarh","Almora"]},{name:"Himachal Pradesh",code:"HP",cities:["Shimla","Dharamshala","Solan","Mandi","Palampur","Baddi","Nahan","Paonta Sahib","Sundarnagar","Chamba"]},{name:"Jammu and Kashmir",code:"JK",cities:["Srinagar","Jammu","Anantnag","Baramulla","Sopore","Kathua","Udhampur","Punch","Rajouri","Kupwara"]},{name:"Goa",code:"GA",cities:["Panaji","Vasco da Gama","Margao","Mapusa","Ponda","Bicholim","Curchorem","Sanquelim","Cuncolim","Quepem"]},{name:"Andhra Pradesh",code:"AP",cities:["Visakhapatnam","Vijayawada","Guntur","Nellore","Kurnool","Rajahmundry","Tirupati","Kakinada","Anantapur","Vizianagaram"]},{name:"Telangana",code:"TS",cities:["Hyderabad","Warangal","Nizamabad","Khammam","Karimnagar","Ramagundam","Mahbubnagar","Nalgonda","Adilabad","Suryapet"]},{name:"Arunachal Pradesh",code:"AR",cities:["Itanagar","Naharlagun","Pasighat","Tezpur","Bomdila","Ziro","Along","Changlang","Tezu","Khonsa"]},{name:"Manipur",code:"MN",cities:["Imphal","Thoubal","Bishnupur","Churachandpur","Ukhrul","Senapati","Tamenglong","Chandel","Jiribam","Kangpokpi"]},{name:"Meghalaya",code:"ML",cities:["Shillong","Tura","Jowai","Nongstoin","Baghmara","Ampati","Resubelpara","Mawkyrwat","Williamnagar","Khliehriat"]},{name:"Mizoram",code:"MZ",cities:["Aizawl","Lunglei","Saiha","Champhai","Kolasib","Serchhip","Mamit","Lawngtlai","Saitual","Khawzawl"]},{name:"Nagaland",code:"NL",cities:["Kohima","Dimapur","Mokokchung","Tuensang","Wokha","Zunheboto","Phek","Kiphire","Longleng","Peren"]},{name:"Sikkim",code:"SK",cities:["Gangtok","Namchi","Geyzing","Mangan","Jorethang","Nayabazar","Rangpo","Singtam","Yuksom","Ravangla"]},{name:"Tripura",code:"TR",cities:["Agartala","Dharmanagar","Udaipur","Kailasahar","Belonia","Khowai","Pratapgarh","Ranir Bazar","Sonamura","Kumarghat"]},{name:"Andaman and Nicobar Islands",code:"AN",cities:["Port Blair","Diglipur","Mayabunder","Rangat","Havelock Island","Neil Island","Car Nicobar","Nancowry","Little Andaman","Baratang"]},{name:"Chandigarh",code:"CH",cities:["Chandigarh"]},{name:"Dadra and Nagar Haveli and Daman and Diu",code:"DN",cities:["Daman","Diu","Silvassa"]},{name:"Lakshadweep",code:"LD",cities:["Kavaratti","Agatti","Minicoy","Amini","Andrott","Kalpeni","Kadmat","Kiltan","Chetlat","Bitra"]},{name:"Puducherry",code:"PY",cities:["Puducherry","Karaikal","Mahe","Yanam"]},{name:"Ladakh",code:"LA",cities:["Leh","Kargil","Nubra","Zanskar","Drass","Khaltse","Nyoma","Durbuk","Khalsi","Turtuk"]}],n=async a=>{try{let r=await fetch("https://api.postalpincode.in/pincode/".concat(a));if(!r.ok)throw Error("Failed to fetch location data");let i=await r.json();if(!i||0===i.length||"Success"!==i[0].Status)throw Error("Invalid pincode or no data found");let e=i[0].PostOffice[0];return{latitude:0,longitude:0,city:e.District,state:e.State,pincode:a,country:"India"}}catch(a){throw Error("Failed to get location from pincode")}},t=()=>e.map(a=>a.name).sort(),o=a=>{let r=e.find(r=>r.name.toLowerCase()===a.toLowerCase());return r?r.cities.sort():[]}},67654:function(a,r,i){"use strict";i.d(r,{Jr:function(){return o},Mq:function(){return s},mw:function(){return e},t6:function(){return t},wi:function(){return n}});let e=()=>new Promise(a=>{if(window.Razorpay){a(!0);return}let r=document.createElement("script");r.src="https://checkout.razorpay.com/v1/checkout.js",r.onload=()=>{console.log("Razorpay SDK loaded successfully"),a(!0)},r.onerror=()=>{console.error("Failed to load Razorpay SDK"),a(!1)},document.body.appendChild(r)}),n=async function(a,r){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(!a||a<=0)throw Error("Invalid amount");if(a<1)throw Error("Minimum order amount is ₹1");console.log("Creating Razorpay order:",{amount:a,receipt:r,notes:i});let e=await fetch("/api/razorpay/create-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:Math.round(100*a),receipt:r,notes:i})});if(!e.ok){let a=await e.json();if(console.error("Razorpay order creation failed:",a),400===e.status)throw Error(a.error||"Invalid order data");if(500===e.status)throw Error("Payment gateway error. Please try again.");throw Error(a.error||"Failed to create payment order")}let n=await e.json();return console.log("Razorpay order created successfully:",n.id),n}catch(a){if(console.error("Error creating Razorpay order:",a),a instanceof Error)throw a;throw Error("Failed to create payment order")}},t=async(a,r)=>{try{let i=await fetch("/api/razorpay/verify-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({razorpay_payment_id:a.razorpay_payment_id,razorpay_order_id:a.razorpay_order_id,razorpay_signature:a.razorpay_signature,address:r.address,cartItems:r.cartItems,shipping:r.shipping})});if(!i.ok){let a=await i.json();throw Error(a.message||"Payment verification failed")}return await i.json()}catch(a){throw console.error("Error verifying payment:",a),a}},o=a=>new Promise((r,i)=>{try{if(!window.Razorpay){i(Error("Razorpay SDK not loaded"));return}new window.Razorpay({...a,handler:a=>{r(a)},modal:{ondismiss:()=>{i(Error("Payment canceled by user"))}}}).open()}catch(a){console.error("Error initializing Razorpay:",a),i(a)}}),s=async(a,r,i)=>{try{let e=await fetch("/api/shipping-rates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pincode:a,cartItems:r,state:i})});if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to get shipping rates")}return await e.json()}catch(a){throw console.error("Error getting shipping rates:",a),a}}}},function(a){a.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,6628,986,4754,5949,1744],function(){return a(a.s=33217)}),_N_E=a.O()}]);