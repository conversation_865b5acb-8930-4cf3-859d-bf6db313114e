(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5949],{33217:function(e,s,t){Promise.resolve().then(t.bind(t,45987))},45987:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return b}});var r=t(57437),a=t(2265),i=t(99376),n=t(29501),d=t(87758),l=t(46714),c=t(3371),o=t(67654),m=t(12381),h=t(40279),p=t(66840),u=a.forwardRef((e,s)=>(0,r.jsx)(p.WV.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));u.displayName="Label";var x=t(93448);function g(e){let{className:s,...t}=e;return(0,r.jsx)(u,{"data-slot":"label",className:(0,x.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}var f=t(15863),y=t(40340),j=t(88226),N=t(4139);function v(e){let{selectedState:s,selectedCity:t,onStateChange:i,onCityChange:n,stateError:d,cityError:l}=e,[c]=(0,a.useState)((0,N.Rh)()),[o,m]=(0,a.useState)([]);return(0,a.useEffect)(()=>{if(s){let e=(0,N.nY)(s);m(e),t&&!e.includes(t)&&n("")}else m([]),n("")},[s,t,n]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"state",children:"State"}),(0,r.jsxs)("select",{id:"state",value:s,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2c2c27] ".concat(d?"border-red-300":"border-gray-300"),children:[(0,r.jsx)("option",{value:"",children:"Select State"}),c.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),d&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"city",children:"City"}),(0,r.jsxs)("select",{id:"city",value:t,onChange:e=>n(e.target.value),disabled:!s||0===o.length,className:"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2c2c27] ".concat(l?"border-red-300":"border-gray-300"," ").concat(s&&0!==o.length?"":"bg-gray-100 cursor-not-allowed"),children:[(0,r.jsx)("option",{value:"",children:"Select City"}),o.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),l&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:l}),s&&0===o.length&&(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"No cities available for selected state"})]})]})}function b(){var e,s;let t=(0,i.useRouter)(),{isAuthenticated:p,isLoading:u}=(0,c.O)(),x=(0,d.useLocalCartStore)(),b=(0,l.p)(),[w,P]=(0,a.useState)(!1),{register:S,handleSubmit:F,watch:E,setValue:A,formState:{errors:C}}=(0,n.cI)({mode:"onChange"});(0,a.useEffect)(()=>{S("state",{required:"State is required"}),S("city",{required:"City is required"})},[S]);let k=E("pincode"),q=E("state"),I=E("city");(0,a.useEffect)(()=>{if(!p){t.push("/sign-in");return}if(0===x.items.length){t.push("/");return}b.setCart(x.items)},[x.items,t,p]),(0,a.useEffect)(()=>{(0,o.mw)()},[]),(0,a.useEffect)(()=>{k&&6===k.length&&q&&p&&b.fetchShippingRates(k,q)},[k,q,p]),(0,a.useEffect)(()=>{k&&6===k.length&&(async()=>{try{let e=await (0,N.v8)(k);e.state&&(A("state",e.state),p&&b.fetchShippingRates(k,e.state)),e.city&&A("city",e.city)}catch(e){console.error("Error fetching location from pincode:",e)}})()},[k,A,p]);let _=async e=>{let s={firstName:e.firstName,lastName:e.lastName,address1:e.address1,address2:e.address2,city:e.city,state:e.state,pincode:e.pincode,phone:e.phone};b.setShippingAddress(s)},z=async()=>{if(!b.shippingAddress){b.setError("Please fill in your shipping address");return}if(!b.selectedShipping){b.setError("Shipping cost not calculated. Please enter a valid pincode.");return}if(0===b.cart.length){b.setError("Your cart is empty");return}if(b.finalAmount<=0){b.setError("Invalid order amount");return}P(!0),b.setProcessingPayment(!0),b.setError(null);try{let e="rzp_live_H1Iyl4j48eSFYj";if(!e)throw Error("Payment gateway not configured. Please contact support.");console.log("Creating Razorpay order for amount:",b.finalAmount);let s=await (0,o.wi)(b.finalAmount,"order_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),{customer_phone:b.shippingAddress.phone,customer_name:"".concat(b.shippingAddress.firstName," ").concat(b.shippingAddress.lastName),shipping_method:b.selectedShipping.name});console.log("Razorpay order created:",s.id),await (0,o.Jr)({key:e,amount:s.amount,currency:s.currency,name:"Ankkor",description:"Order Payment - ".concat(b.cart.length," item(s)"),order_id:s.id,handler:async e=>{console.log("Payment successful, verifying...",e),b.setError(null);try{let s=await (0,o.t6)(e,{address:b.shippingAddress,cartItems:b.cart,shipping:b.selectedShipping});if(console.log("Payment verification result:",s),s.success)x.clearCart(),b.clearCheckout(),t.push("/order-confirmed?id=".concat(s.orderId));else throw Error(s.message||"Payment verification failed")}catch(e){console.error("Payment verification error:",e),b.setError(e instanceof Error?e.message:"Payment verification failed. Please contact support if amount was deducted.")}finally{P(!1),b.setProcessingPayment(!1)}},prefill:{name:"".concat(b.shippingAddress.firstName," ").concat(b.shippingAddress.lastName),contact:b.shippingAddress.phone},theme:{color:"#2c2c27"},modal:{ondismiss:()=>{console.log("Payment modal dismissed"),P(!1),b.setProcessingPayment(!1)}}})}catch(i){var e,s,r,a;console.error("Payment error:",i);let t="Payment failed. Please try again.";(null===(e=i.message)||void 0===e?void 0:e.includes("not configured"))?t=i.message:(null===(s=i.message)||void 0===s?void 0:s.includes("network"))||(null===(r=i.message)||void 0===r?void 0:r.includes("fetch"))?t="Network error. Please check your connection and try again.":(null===(a=i.message)||void 0===a?void 0:a.includes("amount"))?t="Invalid amount. Please refresh and try again.":i.message&&(t=i.message),b.setError(t)}finally{P(!1),b.setProcessingPayment(!1)}};return u?(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[(0,r.jsx)(f.Z,{className:"h-8 w-8 animate-spin"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading..."})]})}):p&&0!==x.items.length?(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-serif mb-8",children:"Checkout"}),b.error&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded",children:b.error}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,r.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,r.jsx)(y.Z,{className:"mr-2 h-5 w-5"}),"Shipping Address"]}),(0,r.jsxs)("form",{onSubmit:F(_),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"firstName",children:"First Name"}),(0,r.jsx)(h.I,{id:"firstName",...S("firstName",{required:"First name is required"}),className:C.firstName?"border-red-300":""}),C.firstName&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C.firstName.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"lastName",children:"Last Name"}),(0,r.jsx)(h.I,{id:"lastName",...S("lastName",{required:"Last name is required"}),className:C.lastName?"border-red-300":""}),C.lastName&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C.lastName.message})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)(g,{htmlFor:"address1",children:"Address Line 1"}),(0,r.jsx)(h.I,{id:"address1",...S("address1",{required:"Address is required"}),className:C.address1?"border-red-300":""}),C.address1&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C.address1.message})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)(g,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),(0,r.jsx)(h.I,{id:"address2",...S("address2")})]}),(0,r.jsx)(v,{selectedState:q||"",selectedCity:I||"",onStateChange:e=>A("state",e),onCityChange:e=>A("city",e),stateError:null===(e=C.state)||void 0===e?void 0:e.message,cityError:null===(s=C.city)||void 0===s?void 0:s.message}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"pincode",children:"Pincode"}),(0,r.jsx)(h.I,{id:"pincode",...S("pincode",{required:"Pincode is required",pattern:{value:/^[0-9]{6}$/,message:"Please enter a valid 6-digit pincode"}}),className:C.pincode?"border-red-300":"",placeholder:"Enter 6-digit pincode"}),C.pincode&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C.pincode.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g,{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)(h.I,{id:"phone",...S("phone",{required:"Phone number is required"}),className:C.phone?"border-red-300":""}),C.phone&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C.phone.message})]})]}),(0,r.jsx)(m.z,{type:"submit",className:"mt-4 w-full bg-[#2c2c27] hover:bg-[#3c3c37] text-white",children:"Save Address & Continue"})]})]}),(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,r.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,r.jsx)(y.Z,{className:"mr-2 h-5 w-5"}),"Shipping Information"]}),b.isLoadingShipping?(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,r.jsx)(f.Z,{className:"h-6 w-6 animate-spin mr-2"}),(0,r.jsx)("span",{children:"Calculating shipping cost..."})]}):b.selectedShipping?(0,r.jsx)("div",{className:"border rounded-lg p-4 bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:b.selectedShipping.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:b.selectedShipping.description}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Estimated delivery: ",b.selectedShipping.estimatedDays]})]}),(0,r.jsx)("div",{className:"text-lg font-medium",children:0===b.selectedShipping.cost?"Free":"₹".concat(b.selectedShipping.cost.toFixed(2))})]})}):(0,r.jsx)("div",{className:"text-gray-500 py-4",children:k&&6===k.length?"Unable to calculate shipping for this pincode":"Enter a valid pincode to calculate shipping cost"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,r.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,r.jsx)(j.Z,{className:"mr-2 h-5 w-5"}),"Payment"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center p-4 border rounded-lg",children:[(0,r.jsx)("input",{type:"radio",id:"razorpay",name:"payment",checked:!0,readOnly:!0,className:"mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"razorpay",className:"font-medium",children:"Razorpay"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Pay securely with credit card, debit card, UPI, or net banking"})]})]}),(0,r.jsx)(m.z,{onClick:z,className:"w-full py-6 bg-[#2c2c27] hover:bg-[#3c3c37] text-white",disabled:w||!b.shippingAddress||!b.selectedShipping||b.isProcessingPayment,children:w||b.isProcessingPayment?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing Payment..."]}):"Proceed to Pay - ₹".concat(b.finalAmount.toFixed(2))})]})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm sticky top-8",children:[(0,r.jsx)("h2",{className:"text-xl font-medium mb-4",children:"Order Summary"}),(0,r.jsxs)("div",{className:"space-y-4",children:[b.cart.map(e=>{var s;return(0,r.jsxs)("div",{className:"flex gap-4 py-2 border-b",children:[(null===(s=e.image)||void 0===s?void 0:s.url)&&(0,r.jsx)("div",{className:"relative h-16 w-16 bg-gray-100 flex-shrink-0",children:(0,r.jsx)("img",{src:e.image.url,alt:e.name,className:"h-full w-full object-cover rounded"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["₹","string"==typeof e.price?parseFloat(e.price).toFixed(2):e.price.toFixed(2)," \xd7 ",e.quantity]})]}),(0,r.jsxs)("div",{className:"text-right",children:["₹",("string"==typeof e.price?parseFloat(e.price)*e.quantity:e.price*e.quantity).toFixed(2)]})]},e.id)}),(0,r.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Subtotal"}),(0,r.jsxs)("span",{children:["₹",b.subtotal.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Shipping"}),(0,r.jsx)("span",{children:b.selectedShipping?0===b.selectedShipping.cost?"Free":"₹".concat(b.selectedShipping.cost.toFixed(2)):"TBD"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-medium pt-2 border-t",children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsxs)("span",{children:["₹",b.finalAmount.toFixed(2)]})]})]})]})]})})]})]}):null}}}]);