(()=>{var e={};e.id=1599,e.ids=[1599],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},67969:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GlobalError:()=>i.Z,__next_app__:()=>x,originalPathname:()=>f,pages:()=>p,routeModule:()=>m,tree:()=>u});var s=r(8179);r(11360),r(7629),r(11930),r(12523);var n=r(23191),o=r(88716),i=r(43315),c=r(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);r.d(t,d);var l=e([s]);s=(l.then?(await l)():l)[0];let u=["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8179)),"E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],p=["E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"],f="/product/[slug]/page",x={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}});a()}catch(e){a(e)}})},72996:(e,t,r)=>{Promise.resolve().then(r.bind(r,97264))},34565:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(69029),s=r.n(a)},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return i}});let a=r(91174),s=r(23078),n=r(92481),o=a._(r(86820));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=n.Image},97264:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var a=r(10326),s=r(17577),n=r(46226),o=r(92148),i=r(86806),c=r(91664),d=r(77321),l=r(76557);let u=(0,l.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),p=(0,l.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),f=(0,l.Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),x=(0,l.Z)("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);var m=r(34565);let g=({product:e})=>{let[t,r]=(0,s.useState)(0),[l,g]=(0,s.useState)(1),[h,y]=(0,s.useState)(null),[v,b]=(0,s.useState)({}),[j,k]=(0,s.useState)(!1),S=(0,i.rY)(),{openCart:w}=(0,d.j)(),{id:N,databaseId:_,name:P,description:R,shortDescription:E,price:M,regularPrice:O,onSale:C,stockStatus:T,image:A,galleryImages:q,attributes:U,type:F,variations:L}=e,{stockData:I,isConnected:z}=function(e,t=!0){let[r,a]=(0,s.useState)({}),n=(0,s.useCallback)(t=>{t.productId===e&&a({stockStatus:t.stockStatus,stockQuantity:t.stockQuantity,availableForSale:t.availableForSale,lastUpdated:t.timestamp})},[e]),{isConnected:o,error:i}=function({productIds:e=[],onStockUpdate:t,enabled:r=!0}={}){let[a,n]=(0,s.useState)(!1),[o,i]=(0,s.useState)(null),[c,d]=(0,s.useState)(null),l=(0,s.useCallback)(()=>{if(!r||0===e.length)return;let a=new URLSearchParams({products:e.join(",")}),s=new EventSource(`/api/stock-updates?${a}`);return s.onopen=()=>{console.log("Stock updates stream connected"),n(!0),d(null)},s.onmessage=e=>{try{let r=JSON.parse(e.data);i(r),"stock_update"===r.type&&(console.log("Stock update received:",r),t?.(r))}catch(e){console.error("Error parsing stock update:",e)}},s.onerror=e=>{console.error("Stock updates stream error:",e),n(!1),d("Connection to stock updates failed"),setTimeout(()=>{s.readyState===EventSource.CLOSED&&l()},5e3)},s},[e,t,r]);return{isConnected:a,lastUpdate:o,error:c,reconnect:l}}({productIds:[e],onStockUpdate:n,enabled:t});return{stockData:r,isConnected:o,error:i}}(_?.toString()||"",!0),Z=I.stockStatus||T,$=I.stockQuantity,D="VARIABLE"===F,G=[A?.sourceUrl?{sourceUrl:A.sourceUrl,altText:A.altText||P}:null,...q?.nodes||[]].filter(Boolean),Q=(e,t)=>{if(b(r=>({...r,[e]:t})),D&&L?.nodes){let r={...v,[e]:t};if(U?.nodes?.every(e=>r[e.name])){let e=L.nodes.find(e=>e.attributes.nodes.every(e=>{let t=r[e.name];return e.value===t}));e?y(e):y(null)}}},V=async()=>{k(!0);try{let e={productId:_.toString(),quantity:l,name:P,price:h?.price||M,image:{url:G[0]?.sourceUrl||"",altText:G[0]?.altText||P}};await S.addToCart(e),w()}catch(e){console.error("Error adding product to cart:",e)}finally{k(!1)}},B="IN_STOCK"!==(Z||T)&&"instock"!==(Z||T),W=!D||D&&h;return a.jsx("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:G[t]?.sourceUrl&&a.jsx(n.default,{src:G[t].sourceUrl,alt:G[t].altText||P,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),G.length>1&&a.jsx("div",{className:"grid grid-cols-5 gap-2",children:G.map((e,s)=>a.jsx("button",{onClick:()=>r(s),className:`relative aspect-square bg-[#f4f3f0] ${t===s?"ring-2 ring-[#2c2c27]":""}`,children:a.jsx(n.default,{src:e.sourceUrl,alt:e.altText||`${P} - Image ${s+1}`,fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},s))})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:P}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-xl font-medium text-[#2c2c27]",children:(h?.price||M).toString().includes("₹")||(h?.price||M).toString().includes("$")||(h?.price||M).toString().includes("€")||(h?.price||M).toString().includes("\xa3")?h?.price||M:`₹${h?.price||M}`}),C&&O&&a.jsx("span",{className:"text-sm line-through text-[#8a8778]",children:O.toString().includes("₹")||O.toString().includes("$")||O.toString().includes("€")||O.toString().includes("\xa3")?O:`₹${O}`})]}),E&&a.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:E}}),D&&U?.nodes&&a.jsx("div",{className:"space-y-4",children:U.nodes.map(e=>(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>a.jsx("button",{onClick:()=>Q(e.name,t),className:`px-4 py-2 border ${v[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"}`,children:t},t))})]},e.name))}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300",children:[a.jsx("button",{onClick:()=>g(e=>e>1?e-1:1),disabled:l<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:a.jsx(u,{className:"h-4 w-4"})}),a.jsx("span",{className:"px-4 py-2 border-x border-gray-300",children:l}),a.jsx("button",{onClick:()=>g(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:a.jsx(p,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"font-medium",children:"Availability: "}),a.jsx("span",{className:B?"text-red-600":"text-green-600",children:B?"Out of Stock":"In Stock"}),z&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-green-600",children:[a.jsx(f,{className:"h-3 w-3"}),a.jsx("span",{children:"Live"})]}),!z&&I.lastUpdated&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[a.jsx(x,{className:"h-3 w-3"}),a.jsx("span",{children:"Offline"})]})]}),null!=$&&a.jsx("div",{className:"text-xs text-gray-600 mt-1",children:$>0?(0,a.jsxs)("span",{children:[$," items available"]}):a.jsx("span",{className:"text-red-600",children:"No items in stock"})}),I.lastUpdated&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Last updated: ",new Date(I.lastUpdated).toLocaleTimeString()]})]}),(0,a.jsxs)(o.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,a.jsxs)(c.z,{onClick:V,disabled:B||j||!W,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[a.jsx(m.Z,{className:"h-5 w-5"}),j?"Adding...":"Add to Cart"]}),D&&!W&&!B&&a.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),R&&(0,a.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[a.jsx("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),a.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:R}})]})]})]})})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var a=r(10326);r(17577);var s=r(34214),n=r(79360),o=r(51223);let i=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:r,asChild:n=!1,...c}){let d=n?s.g7:"button";return a.jsx(d,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...c})}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(41135),s=r(31009);function n(...e){return(0,s.m6)((0,a.W)(e))}},58585:(e,t,r)=>{"use strict";var a=r(61085);r.o(a,"notFound")&&r.d(t,{notFound:function(){return a.notFound}}),r.o(a,"redirect")&&r.d(t,{redirect:function(){return a.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return o},RedirectType:function(){return a.RedirectType},notFound:function(){return s.notFound},permanentRedirect:function(){return a.permanentRedirect},redirect:function(){return a.redirect}});let a=r(83953),s=r(16399);class n extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class o extends URLSearchParams{append(){throw new n}delete(){throw new n}set(){throw new n}sort(){throw new n}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return s},notFound:function(){return a}});let r="NEXT_NOT_FOUND";function a(){let e=Error(r);throw e.digest=r,e}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var a;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return a},getRedirectError:function(){return c},getRedirectStatusCodeFromError:function(){return x},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return d}});let s=r(54580),n=r(72934),o=r(8586),i="NEXT_REDIRECT";function c(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let a=Error(i);a.digest=i+";"+t+";"+e+";"+r+";";let n=s.requestAsyncStorage.getStore();return n&&(a.mutableCookies=n.mutableCookies),a}function d(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function l(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,a,s]=e.digest.split(";",4),n=Number(s);return t===i&&("replace"===r||"push"===r)&&"string"==typeof a&&!isNaN(n)&&n in o.RedirectStatusCode}function p(e){return u(e)?e.digest.split(";",3)[2]:null}function f(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function x(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(a||(a={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8179:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>l,generateMetadata:()=>d});var s=r(19510),n=r(58585),o=r(19910),i=r(80151),c=e([o]);async function d({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);if(!e)return{title:"Product Not Found | Ankkor",description:"The requested product could not be found."};return{title:`${e.name} | Ankkor`,description:e.shortDescription||e.description||"Luxury menswear from Ankkor.",openGraph:{images:e.image?[{url:e.image.sourceUrl,alt:e.name}]:[]}}}catch(e){return console.error("Error generating product metadata:",e),{title:"Product | Ankkor",description:"Luxury menswear from Ankkor."}}}async function l({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);return e||(0,n.notFound)(),s.jsx(i.Z,{product:e})}catch(e){console.error("Error fetching product:",e),(0,n.notFound)()}}o=(c.then?(await c)():c)[0],a()}catch(e){a(e)}})},80151:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\product\ProductDetail.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1057,4766,4868,2481,2325,5436,9910],()=>r(67969));module.exports=a})();