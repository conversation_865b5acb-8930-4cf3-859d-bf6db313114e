"use strict";(()=>{var e={};e.id=7546,e.ids=[7546],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},90935:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>f,requestAsyncStorage:()=>h,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var i={};r.r(i),r.d(i,{POST:()=>p});var s=r(49303),a=r(88716),n=r(60670),o=r(87070);async function p(e){try{let{pincode:t,cartItems:r,state:i}=await e.json();if(!t||!r||!Array.isArray(r))return o.NextResponse.json({error:"Invalid request data"},{status:400});if(!/^[0-9]{6}$/.test(t))return o.NextResponse.json({error:"Invalid pincode format"},{status:400});let s=process.env.SHIPPING_PROVIDER||"woocommerce",a=[];return a="woocommerce"===s?await d(t,r):"delhivery"===s?await c(t,r):await u(t,r,i),o.NextResponse.json(a)}catch(e){return console.error("Shipping rates error:",e),o.NextResponse.json({error:e.message||"Failed to calculate shipping rates"},{status:500})}}async function d(e,t){try{let r="https://maroon-lapwing-781450.hostingersite.com",i=process.env.WOOCOMMERCE_CONSUMER_KEY,s=process.env.WOOCOMMERCE_CONSUMER_SECRET;if(!r||!i||!s)throw Error("WooCommerce credentials not configured");let a=t.reduce((e,t)=>{let r="string"==typeof t.price?parseFloat(t.price):t.price;return e+r*t.quantity},0);t.reduce((e,t)=>e+(t.weight||.5)*t.quantity,0);let n=Buffer.from(`${i}:${s}`).toString("base64"),o=await fetch(`${r}/wp-json/wc/v3/shipping/zones`,{headers:{Authorization:`Basic ${n}`}});if(!o.ok)throw Error("Failed to fetch shipping zones");let p=await o.json(),d=[];for(let e of p){if(0===e.id)continue;let t=await fetch(`${r}/wp-json/wc/v3/shipping/zones/${e.id}/methods`,{headers:{Authorization:`Basic ${n}`}});if(t.ok){for(let r of(await t.json()))if(r.enabled){let t=0;if("flat_rate"===r.method_id)t=parseFloat(r.settings?.cost?.value||"0");else if("free_shipping"===r.method_id){let e=parseFloat(r.settings?.min_amount?.value||"0");t=a>=e?0:parseFloat(r.settings?.cost?.value||"50")}else"local_pickup"===r.method_id&&(t=parseFloat(r.settings?.cost?.value||"0"));d.push({id:`${e.id}_${r.instance_id}`,name:0===t?"Free Shipping":"Standard Shipping",cost:t,description:0===t?"Free shipping on orders above minimum amount":"Standard delivery across India",estimatedDays:"5-7 days"});break}if(d.length>0)break}}if(0===d.length)return u(e,t,state);return d}catch(r){return console.error("WooCommerce shipping error:",r),u(e,t)}}async function c(e,t){try{let e=t.reduce((e,t)=>e+(t.weight||.5)*t.quantity,0);return[{id:"delhivery_standard",name:"Standard Shipping",cost:Math.max(50,10*e),description:"Standard delivery across India",estimatedDays:"5-7 days"}]}catch(r){return console.error("Delhivery shipping error:",r),u(e,t)}}async function u(e,t,i){let{calculateShippingCost:s,getLocationFromPincode:a}=await r.e(5630).then(r.bind(r,85630)),n=t.reduce((e,t)=>e+("string"==typeof t.price?parseFloat(t.price):t.price)*t.quantity,0),o=i||"",p=99;if(i)p=s(o=i,n);else try{o=(await a(e)).state||"",p=s(o,n)}catch(e){console.error("Error getting location from pincode:",e),p=n>2999?0:99}let d=[],c=0===p?"Free Shipping":"Standard Shipping",u=0===p?"Free shipping on orders above ₹2999":"Standard delivery across India";return d.push({id:"standard",name:c,cost:p,description:u,estimatedDays:"5-7 days",state:o}),["110001","400001","560001","600001","700001"].includes(e)?(d.push({id:"express",name:"Express Shipping",cost:150,description:"Delivered in 2-3 business days",estimatedDays:"2-3 days"}),d.push({id:"same_day",name:"Same Day Delivery",cost:300,description:"Delivered today before 9 PM",estimatedDays:"Today"})):d.push({id:"express",name:"Express Shipping",cost:200,description:"Delivered in 3-4 business days",estimatedDays:"3-4 days"}),d}let l=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/shipping-rates/route",pathname:"/api/shipping-rates",filename:"route",bundlePath:"app/api/shipping-rates/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\shipping-rates\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:g}=l,y="/api/shipping-rates/route";function f(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,5972],()=>r(90935));module.exports=i})();