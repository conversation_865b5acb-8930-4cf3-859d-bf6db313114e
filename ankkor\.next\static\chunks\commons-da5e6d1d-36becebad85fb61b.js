"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7158],{50749:function(e,t,a){var s=a(57437),r=a(2265),i=a(99376),l=a(29501),c=a(15863),o=a(3371);t.default=e=>{let{mode:t,redirectUrl:a="/"}=e,n=(0,i.useRouter)(),{refreshCustomer:d}=(0,o.O)(),[m,u]=(0,r.useState)(!1),[x,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(null),[f,b]=(0,r.useState)(null),w="login"===t,{register:N,handleSubmit:j,watch:v,formState:{errors:y}}=(0,l.cI)({mode:"onBlur"}),S=v("password",""),C=async e=>{u(!0),h(null),g(null),b(null);try{if(w){console.log("Attempting login with:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),s=await t.json();s.success?(g("Login successful! Redirecting..."),setTimeout(async()=>{await d(),n.push(a),n.refresh()},500)):h(s.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),s=await t.json();s.success?(g("Registration successful! Redirecting..."),await d(),setTimeout(()=>{n.push(a),n.refresh()},1e3)):h(s.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),h(e.message||"An error occurred during authentication"),g(null)}finally{u(!1)}};return(0,s.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[(0,s.jsx)("h2",{className:"text-2xl font-serif mb-6 text-center",children:w?"Sign In to Your Account":"Create an Account"}),x&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:x}),p&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:p}),f&&!1,(0,s.jsxs)("form",{onSubmit:j(C),className:"space-y-4",children:[!w&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),(0,s.jsx)("input",{id:"firstName",type:"text",className:"w-full p-2 border ".concat(y.firstName?"border-red-500":"border-gray-300"),...N("firstName",{required:"First name is required"})}),y.firstName&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:y.firstName.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),(0,s.jsx)("input",{id:"lastName",type:"text",className:"w-full p-2 border ".concat(y.lastName?"border-red-500":"border-gray-300"),...N("lastName",{required:"Last name is required"})}),y.lastName&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:y.lastName.message})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,s.jsx)("input",{id:"email",type:"email",className:"w-full p-2 border ".concat(y.email?"border-red-500":"border-gray-300"),...N("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),y.email&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:y.email.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,s.jsx)("input",{id:"password",type:"password",className:"w-full p-2 border ".concat(y.password?"border-red-500":"border-gray-300"),...N("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),y.password&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:y.password.message})]}),!w&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirmPassword",type:"password",className:"w-full p-2 border ".concat(y.confirmPassword?"border-red-500":"border-gray-300"),...N("confirmPassword",{required:"Please confirm your password",validate:e=>e===S||"Passwords do not match"})}),y.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:y.confirmPassword.message})]}),(0,s.jsx)("button",{type:"submit",disabled:m,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:m?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsx)(c.Z,{className:"animate-spin mr-2 h-4 w-4"}),w?"Signing in...":"Creating account..."]}):w?"Sign In":"Create Account"})]}),w?(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})}},18686:function(e,t,a){a.d(t,{j:function(){return c}});var s=a(57437),r=a(2265),i=a(87758);let l=(0,r.createContext)(void 0),c=()=>{let e=(0,r.useContext)(l);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e};t.default=e=>{let{children:t}=e,a=(0,i.useLocalCartStore)(),[c,o]=(0,r.useState)(!1),n={openCart:()=>o(!0),closeCart:()=>o(!1),toggleCart:()=>o(e=>!e),isOpen:c,itemCount:a.itemCount};return(0,s.jsx)(l.Provider,{value:n,children:t})}},62670:function(e,t,a){var s=a(57437),r=a(2265),i=a(27648),l=a(43886),c=a(88997),o=a(15863),n=a(42449),d=a(87758),m=a(92371),u=a(3371),x=a(18686),h=a(57152),p=a(70597),g=a(11738);let f=e=>{if("number"==typeof e)return e.toString();if(!e)return"0";let t=parseFloat(e.toString().replace(/[^\d.-]/g,""));return isNaN(t)?"0":t.toString()};t.Z=e=>{let{id:t,name:a,price:b,image:w,slug:N,material:j,isNew:v=!1,stockStatus:y="IN_STOCK",compareAtPrice:S=null,regularPrice:C=null,salePrice:k=null,onSale:A=!1,currencySymbol:P=p.J6,currencyCode:I=p.EJ,shortDescription:O,type:F}=e,[L,T]=(0,r.useState)(!1),E=(0,d.useLocalCartStore)(),{openCart:_}=(0,x.j)(),{addToWishlist:Z,isInWishlist:q,removeFromWishlist:R}=(0,m.useWishlistStore)(),{isAuthenticated:H}=(0,u.O)(),$=q(t),D=async e=>{if(e.preventDefault(),e.stopPropagation(),!t||""===t){console.error("Cannot add to cart: Missing product ID for product",a),g.Am.error("Cannot add to cart: Invalid product");return}if(!L){T(!0),console.log("Adding product to cart: ".concat(a," (ID: ").concat(t,")"));try{await E.addToCart({productId:t,quantity:1,name:a,price:b,image:{url:w,altText:a}}),g.Am.success("".concat(a," added to cart!")),_()}catch(e){console.error("Failed to add ".concat(a," to cart:"),e),g.Am.error("Failed to add item to cart. Please try again.")}finally{T(!1)}}},J=e=>{e.preventDefault(),e.stopPropagation(),$?(R(t),g.Am.success("Removed from wishlist")):(Z({id:t,name:a,price:f(b),image:w,handle:N,material:j||"Material not specified",variantId:t}),H?g.Am.success("Added to your wishlist"):g.Am.success("Added to wishlist (saved locally)"))},K=S&&parseFloat(S)>parseFloat(b)?Math.round((parseFloat(S)-parseFloat(b))/parseFloat(S)*100):null,M="IN_STOCK"!==y;return(0,s.jsxs)(l.E.div,{className:"group relative",whileHover:{y:-5},transition:{duration:.3},children:[(0,s.jsxs)(i.default,{href:"/product/".concat(N),className:"block",children:[(0,s.jsxs)("div",{className:"relative overflow-hidden mb-4",children:[(0,s.jsx)("div",{className:"aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden",children:(0,s.jsx)(h.Z,{src:w,alt:a,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",animate:!0,className:"h-full"})}),(0,s.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,s.jsx)(l.E.button,{onClick:J,className:"p-2 rounded-none ".concat($?"bg-[#2c2c27]":"bg-[#f8f8f5]"),whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":$?"Remove from wishlist":"Add to wishlist",children:(0,s.jsx)(c.Z,{className:"h-5 w-5 ".concat($?"text-[#f4f3f0] fill-current":"text-[#2c2c27]")})}),(0,s.jsx)(l.E.button,{onClick:D,className:"p-2 rounded-none ".concat(M||L?"bg-gray-400 cursor-not-allowed":"bg-[#2c2c27]"," text-[#f4f3f0]"),whileHover:M||L?{}:{scale:1.05},whileTap:M||L?{}:{scale:.95},"aria-label":M?"Out of stock":L?"Adding to cart...":"Add to cart",disabled:M||L,children:L?(0,s.jsx)(o.Z,{className:"h-5 w-5 animate-spin"}):(0,s.jsx)(n.Z,{className:"h-5 w-5"})})]}),v&&(0,s.jsx)("div",{className:"absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"New"}),M&&(0,s.jsx)("div",{className:"absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"Out of Stock"}),!M&&K&&(0,s.jsxs)("div",{className:"absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:[K,"% Off"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2",children:a}),j&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs",children:j}),F&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs capitalize",children:F.toLowerCase().replace("_"," ")}),O&&(0,s.jsx)("p",{className:"text-[#5c5c52] text-xs line-clamp-2",dangerouslySetInnerHTML:{__html:O.replace(/<[^>]*>/g,"")}}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 product-card-price",children:[(0,s.jsx)("p",{className:"text-[#2c2c27] font-medium",children:A&&k?k.toString().includes("₹")||k.toString().includes("$")||k.toString().includes("€")||k.toString().includes("\xa3")?k:"".concat(P).concat(k):b.toString().includes("₹")||b.toString().includes("$")||b.toString().includes("€")||b.toString().includes("\xa3")?b:"".concat(P).concat(b)}),A&&C&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:C.toString().includes("₹")||C.toString().includes("$")||C.toString().includes("€")||C.toString().includes("\xa3")?C:"".concat(P).concat(C)}),!A&&S&&parseFloat(S.toString().replace(/[₹$€£]/g,""))>parseFloat(b.toString().replace(/[₹$€£]/g,""))&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:S.toString().includes("₹")||S.toString().includes("$")||S.toString().includes("€")||S.toString().includes("\xa3")?S:"".concat(P).concat(S)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:"IN_STOCK"===y?(0,s.jsx)("span",{className:"text-green-600 text-xs font-medium",children:"✓ In Stock"}):"OUT_OF_STOCK"===y?(0,s.jsx)("span",{className:"text-red-600 text-xs font-medium",children:"✗ Out of Stock"}):"ON_BACKORDER"===y?(0,s.jsx)("span",{className:"text-orange-600 text-xs font-medium",children:"⏳ Backorder"}):(0,s.jsx)("span",{className:"text-gray-600 text-xs font-medium",children:"? Unknown"})}),A&&(0,s.jsx)("span",{className:"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium",children:"Sale"})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsx)(l.E.button,{onClick:D,className:"w-full py-3 px-4 transition-all duration-200 ".concat(M||L?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#1a1a17]"),whileHover:M||L?{}:{scale:1.02},whileTap:M||L?{}:{scale:.98},"aria-label":M?"Out of stock":L?"Adding to cart...":"Add to cart",disabled:M||L,children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[L?(0,s.jsx)(o.Z,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(n.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:M?"Out of Stock":L?"Adding...":"Add to Cart"})]})}),(0,s.jsx)(l.E.button,{onClick:J,className:"w-full py-3 px-4 border transition-all duration-200 ".concat($?"bg-[#2c2c27] text-[#f4f3f0] border-[#2c2c27]":"bg-transparent text-[#2c2c27] border-[#2c2c27] hover:bg-[#2c2c27] hover:text-[#f4f3f0]"),whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":$?"Remove from wishlist":"Add to wishlist",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(c.Z,{className:"h-4 w-4 ".concat($?"fill-current":"")}),(0,s.jsx)("span",{className:"text-sm font-medium",children:$?"In Wishlist":"Add to Wishlist"})]})})]})]})}},3371:function(e,t,a){a.d(t,{CustomerProvider:function(){return o},O:function(){return c}});var s=a(57437),r=a(2265),i=a(14362);let l=(0,r.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:async()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),c=()=>(0,r.useContext)(l),o=e=>{let{children:t}=e,a=(0,i.a)(),r=async e=>{await a.login(e.email,e.password)},c=async e=>{await a.register(e)},o=async()=>{await a.logout()},n=async e=>await a.updateProfile(e),d=async()=>{await a.refreshSession()},m={customer:a.user,isLoading:a.isLoading,isAuthenticated:a.isAuthenticated,token:a.token,login:r,register:c,logout:o,updateProfile:n,error:a.error,refreshCustomer:d};return(0,s.jsx)(l.Provider,{value:m,children:t})}},64528:function(e,t,a){a.d(t,{Gd:function(){return c}});var s=a(57437),r=a(2265),i=a(59625),l=a(89134);let c=(0,i.Ue)()((0,l.tJ)(e=>({isLaunchingSoon:!1,setIsLaunchingSoon:t=>{e({isLaunchingSoon:t})}}),{name:"ankkor-launch-state",skipHydration:!0,storage:{getItem:e=>{try{return localStorage.getItem(e)}catch(e){return console.error("localStorage.getItem error:",e),null}},setItem:(e,t)=>{try{localStorage.setItem(e,t)}catch(e){console.error("localStorage.setItem error:",e)}},removeItem:e=>{try{localStorage.removeItem(e)}catch(e){console.error("localStorage.removeItem error:",e)}}}})),o=(0,r.createContext)(void 0);t.default=e=>{let{children:t}=e,a=c(),[i,l]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{c.persist.rehydrate(),l(!0)},[]),(0,s.jsx)(o.Provider,{value:a,children:t})}},6658:function(e,t,a){a.d(t,{r:function(){return o}});var s=a(57437),r=a(2265),i=a(99376),l=a(29456);let c=(0,r.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),o=()=>(0,r.useContext)(c),n={"/collection":"fabric","/collection/shirts":"fabric","/collection/polos":"fabric","/product":"thread","/about":"button","/customer-service":"button","/account":"thread","/wishlist":"thread"},d=e=>{let t,{setIsLoading:a,setVariant:s}=e,l=(0,i.usePathname)();try{t=(0,i.useSearchParams)()}catch(e){t=null}return(0,r.useEffect)(()=>{a(!0),s(n["/"+l.split("/")[1]]||n[l]||"thread");let e=setTimeout(()=>{a(!1)},1200);return()=>clearTimeout(e)},[l,t,a,s]),null},m=()=>(0,s.jsx)("div",{className:"hidden",children:"Loading route..."});t.default=e=>{let{children:t}=e,[a,i]=(0,r.useState)(!1),[o,n]=(0,r.useState)("thread");return(0,s.jsxs)(c.Provider,{value:{isLoading:a,setLoading:i,variant:o,setVariant:n},children:[(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)(m,{}),children:(0,s.jsx)(d,{setIsLoading:i,setVariant:n})}),t,(0,s.jsx)(l.Z,{isLoading:a,variant:o})]})}}}]);