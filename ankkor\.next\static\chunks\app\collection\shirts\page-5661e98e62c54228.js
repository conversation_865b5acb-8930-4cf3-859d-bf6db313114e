(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9153],{98214:function(o,e,t){Promise.resolve().then(t.bind(t,22313))},22313:function(o,e,t){"use strict";t.r(e),t.d(e,{default:function(){return u}});var l=t(57437),i=t(2265),n=t(33145),c=t(43886),s=t(62670),r=t(3697),a=t(82372),d=t(82429);function u(){let[o,e]=(0,i.useState)([]),[u,g]=(0,i.useState)(!0),[v,h]=(0,i.useState)(null),[m,p]=(0,i.useState)(null);(0,r.Z)(u,"fabric"),(0,i.useEffect)(()=>{(async()=>{try{var o,l,i,n,c,s,r,d,u,v,m,f,x,y,D,b,C,N;g(!0),h(null),console.log("\uD83D\uDD0D Starting to fetch shirts from WooCommerce...");let w=null;try{console.log("\uD83E\uDDEA Testing WooCommerce connection...");let{testWooCommerceConnection:o}=await Promise.resolve().then(t.bind(t,82372));w=await o(),console.log("\uD83D\uDD17 Connection test result:",w)}catch(o){console.log("❌ Failed to test connection:",o)}let j=null;try{console.log("\uD83D\uDCCB Fetching all categories to debug...");let{getAllCategories:o}=await Promise.resolve().then(t.bind(t,82372));j=await o(50),console.log("\uD83D\uDCC2 Available categories:",null==j?void 0:j.map(o=>({name:o.name,slug:o.slug,id:o.id,count:o.count})))}catch(o){console.log("❌ Failed to fetch categories:",o)}let P=null,S="";try{console.log('\uD83D\uDCCB Attempting to fetch with category slug: "shirts"'),P=await (0,a.getCategoryProducts)("shirts",{first:100}),S="slug: shirts",(null==P?void 0:null===(y=P.products)||void 0===y?void 0:null===(x=y.nodes)||void 0===x?void 0:x.length)>0?console.log("✅ Success with method 1 (slug: shirts)"):console.log("⚠️ Method 1 returned empty or null:",P)}catch(o){console.log("❌ Method 1 failed:",o)}if(!(null==P?void 0:null===(l=P.products)||void 0===l?void 0:null===(o=l.nodes)||void 0===o?void 0:o.length))for(let o of["shirt","Shirts","SHIRTS","men-shirts","mens-shirts","clothing","apparel"])try{if(console.log('\uD83D\uDCCB Attempting to fetch with category: "'.concat(o,'"')),P=await (0,a.getCategoryProducts)(o,{first:100}),S="slug: ".concat(o),(null==P?void 0:null===(b=P.products)||void 0===b?void 0:null===(D=b.nodes)||void 0===D?void 0:D.length)>0){console.log("✅ Success with alternative name: ".concat(o));break}console.log("⚠️ No products found for category: ".concat(o))}catch(e){console.log("❌ Failed with ".concat(o,":"),e)}if(!(null==P?void 0:null===(n=P.products)||void 0===n?void 0:null===(i=n.nodes)||void 0===i?void 0:i.length)&&(null==j?void 0:j.length)>0){console.log("\uD83D\uDCCB Searching for shirt-related categories in available categories...");let o=j.find(o=>{var e,t;let l=(null===(e=o.name)||void 0===e?void 0:e.toLowerCase())||"",i=(null===(t=o.slug)||void 0===t?void 0:t.toLowerCase())||"";return l.includes("shirt")||i.includes("shirt")||l.includes("clothing")||i.includes("clothing")||l.includes("apparel")||i.includes("apparel")});if(o){console.log("\uD83D\uDCCB Found potential shirt category: ".concat(o.name," (").concat(o.slug,")"));try{P=await (0,a.getCategoryProducts)(o.slug,{first:100}),S="found category: ".concat(o.slug),(null==P?void 0:null===(N=P.products)||void 0===N?void 0:null===(C=N.nodes)||void 0===C?void 0:C.length)>0&&console.log("✅ Success with found category: ".concat(o.slug))}catch(e){console.log("❌ Failed with found category ".concat(o.slug,":"),e)}}}if(!(null==P?void 0:null===(s=P.products)||void 0===s?void 0:null===(c=s.nodes)||void 0===c?void 0:c.length))try{console.log("\uD83D\uDCCB Attempting to fetch all products and filter by keywords...");let{getAllProducts:o}=await Promise.resolve().then(t.bind(t,82372)),e=await o(100);if(S="all products filtered by keywords",(null==e?void 0:e.length)>0){let o=e.filter(o=>{var e,t,l,i,n;let c=(null===(e=o.name)||void 0===e?void 0:e.toLowerCase())||(null===(t=o.title)||void 0===t?void 0:t.toLowerCase())||"",s=(null===(l=o.description)||void 0===l?void 0:l.toLowerCase())||(null===(i=o.shortDescription)||void 0===i?void 0:i.toLowerCase())||"",r=(null===(n=o.productCategories)||void 0===n?void 0:n.nodes)||o.categories||[],a=["shirt","formal","casual","dress","button","collar","sleeve"].some(o=>c.includes(o)||s.includes(o)),d=r.some(o=>{var e,t;let l=(null===(e=o.name)||void 0===e?void 0:e.toLowerCase())||(null===(t=o.slug)||void 0===t?void 0:t.toLowerCase())||"";return l.includes("shirt")||l.includes("clothing")||l.includes("apparel")});return a||d});P={products:{nodes:o}},console.log("✅ Filtered ".concat(o.length," shirt products from all products"))}}catch(o){console.log("❌ Method 4 failed:",o)}if(p({fetchMethod:S,totalProducts:(null==P?void 0:null===(d=P.products)||void 0===d?void 0:null===(r=d.nodes)||void 0===r?void 0:r.length)||0,connectionTest:w||"No connection test performed",availableCategories:(null==j?void 0:j.map(o=>({name:o.name,slug:o.slug,count:o.count})))||[],categoryData:P?JSON.stringify(P,null,2):"No data",timestamp:new Date().toISOString()}),console.log("\uD83D\uDCCA Debug Info:",{fetchMethod:S,totalProducts:(null==P?void 0:null===(v=P.products)||void 0===v?void 0:null===(u=v.nodes)||void 0===u?void 0:u.length)||0,hasData:!!P,hasProducts:!!(null==P?void 0:P.products),hasNodes:!!(null==P?void 0:null===(m=P.products)||void 0===m?void 0:m.nodes),availableCategories:(null==j?void 0:j.length)||0}),!P||!(null===(f=P.products)||void 0===f?void 0:f.nodes)||0===P.products.nodes.length){console.log("❌ No shirt products found in any category"),h("No shirt products found using method: ".concat(S,". Available categories: ").concat((null==j?void 0:j.map(o=>o.name).join(", "))||"None found",". Please check your WooCommerce shirts category setup.")),g(!1);return}let k=P.products.nodes;console.log("\uD83D\uDCE6 Found ".concat(k.length," products, normalizing..."));let _=k.map((o,e)=>{try{console.log("\uD83D\uDD04 Normalizing product ".concat(e+1,":"),o.name||o.title);let t=(0,a.Op)(o);if(t)return t.currencyCode="INR",console.log("✅ Successfully normalized: ".concat(t.title)),t;return console.log("⚠️ Failed to normalize product: ".concat(o.name||o.title)),null}catch(o){return console.error("❌ Error normalizing product ".concat(e+1,":"),o),null}}).filter(Boolean);console.log("\uD83C\uDF89 Successfully processed ".concat(_.length," shirt products")),console.log("\uD83D\uDCE6 Setting products:",_.map(o=>{var e,t;return{title:o.title,price:null===(t=o.priceRange)||void 0===t?void 0:null===(e=t.minVariantPrice)||void 0===e?void 0:e.amount,id:o.id}})),e(_)}catch(o){console.error("\uD83D\uDCA5 Critical error fetching products:",o),h("Failed to load products from WooCommerce: ".concat(o instanceof Error?o.message:"Unknown error"))}finally{g(!1)}})()},[]);let f={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5}},exit:{opacity:0,y:20,transition:{duration:.3}}};return(0,l.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[(0,l.jsx)("div",{className:"container mx-auto px-4 mb-12",children:(0,l.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Shirts Collection"}),(0,l.jsx)("p",{className:"text-[#5c5c52] mb-8",children:"Discover our meticulously crafted shirts, designed with premium fabrics and impeccable attention to detail."})]})}),(0,l.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[(0,l.jsx)(n.default,{src:"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?q=80",alt:"Ankkor Shirts Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center text-white",children:[(0,l.jsx)("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Signature Shirts"}),(0,l.jsx)("p",{className:"text-lg max-w-xl mx-auto",children:"Impeccably tailored for the perfect fit"})]})})]}),(0,l.jsxs)("div",{className:"container mx-auto px-4",children:[v&&(0,l.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded",children:[(0,l.jsx)("p",{className:"font-semibold",children:"Error loading shirts:"}),(0,l.jsx)("p",{children:v}),(0,l.jsx)("p",{className:"text-sm mt-2",children:"Please check your WooCommerce configuration and ensure you have products in the 'shirts' category."}),m&&(0,l.jsxs)("details",{className:"mt-4",children:[(0,l.jsx)("summary",{className:"cursor-pointer text-sm font-semibold",children:"Debug Information"}),(0,l.jsx)("pre",{className:"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40",children:JSON.stringify(m,null,2)})]})]}),u&&(0,l.jsxs)("div",{className:"text-center py-16",children:[(0,l.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#2c2c27]"}),(0,l.jsx)("p",{className:"mt-4 text-[#5c5c52]",children:"Loading shirts..."})]}),(0,l.jsx)("div",{className:"flex justify-end items-center mb-8",children:(0,l.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[o.length," products"]})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[(0,l.jsx)("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"Shirts Collection"}),(0,l.jsxs)("div",{className:"text-[#5c5c52]",children:[o.length," products"]})]}),!u&&o.length>0&&(0,l.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:o.map(o=>{var e,t,i,n,r,u,g,v,h,m,p;let x="",y=!1;try{if(o.variants&&o.variants.length>0){let e=o.variants[0];if(e&&e.id){if(x=e.id,y=!0,!x.startsWith("gid://shopify/ProductVariant/")){let e=x.replace(/\D/g,"");e?x="gid://shopify/ProductVariant/".concat(e):(console.warn("Cannot parse variant ID for product ".concat(o.title,": ").concat(x)),y=!1)}console.log("Product ".concat(o.title," using variant ID: ").concat(x))}}if(!y&&o.id&&o.id.includes("/")){let e=o.id.split("/"),t=e[e.length-1];t&&/^\d+$/.test(t)&&(x="gid://shopify/ProductVariant/".concat(t),console.warn("Using fallback variant ID for ".concat(o.title,": ").concat(x)),y=!0)}}catch(e){console.error("Error processing variant for product ".concat(o.title,":"),e),y=!1}return y||console.error("No valid variant ID found for product: ".concat(o.title)),(0,l.jsx)(c.E.div,{variants:f,initial:"initial",animate:"animate",exit:"exit",layout:!0,children:(0,l.jsx)(s.Z,{id:o.id,name:o.title,slug:o.handle,price:(null===(e=o._originalWooProduct)||void 0===e?void 0:e.salePrice)||(null===(t=o._originalWooProduct)||void 0===t?void 0:t.price)||(null===(n=o.priceRange)||void 0===n?void 0:null===(i=n.minVariantPrice)||void 0===i?void 0:i.amount)||"0",image:(null===(r=o.images[0])||void 0===r?void 0:r.url)||"",material:(0,a.mJ)(o,"custom_material",void 0,"Premium Fabric"),isNew:!0,stockStatus:(null===(u=o._originalWooProduct)||void 0===u?void 0:u.stockStatus)||"IN_STOCK",compareAtPrice:o.compareAtPrice,regularPrice:null===(g=o._originalWooProduct)||void 0===g?void 0:g.regularPrice,salePrice:null===(v=o._originalWooProduct)||void 0===v?void 0:v.salePrice,onSale:(null===(h=o._originalWooProduct)||void 0===h?void 0:h.onSale)||!1,currencySymbol:(0,d.jK)(o.currencyCode),currencyCode:o.currencyCode||"INR",shortDescription:null===(m=o._originalWooProduct)||void 0===m?void 0:m.shortDescription,type:null===(p=o._originalWooProduct)||void 0===p?void 0:p.type})},o.id)})}),!u&&0===o.length&&!v&&(0,l.jsx)("div",{className:"text-center py-16",children:(0,l.jsx)("p",{className:"text-[#5c5c52] mb-4",children:"No products found."})})]})]})]})}}},function(o){o.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,6628,986,4754,1744],function(){return o(o.s=98214)}),_N_E=o.O()}]);