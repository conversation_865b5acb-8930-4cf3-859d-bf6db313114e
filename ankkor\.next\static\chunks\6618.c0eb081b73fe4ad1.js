"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6618],{36618:function(e,t,r){r.r(t),r.d(t,{default:function(){return q}});var a=r(57437),s=r(2265),c=r(33145),l=r(99376),i=r(48131),n=r(43886),o=r(42449),d=r(32489),m=r(76858),u=r(63639),x=r(82431),h=r(75395),p=r(21047),g=r(99397),f=r(18930),y=r(87758),j=r(82372),b=r(29658),N=r(70597),w=r(12381),v=r(15863),C=e=>{let{onClick:t,isDisabled:r=!1,text:c="Proceed to Checkout",loadingText:l="Processing..."}=e,[i,o]=(0,s.useState)(!1),[d,m]=(0,s.useState)(null),u=async()=>{if(!r&&!i){o(!0),m(null);try{await t()}catch(e){console.error("Checkout button error:",e),m(e instanceof Error?e.message:"An error occurred"),o(!1)}}};return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)(n.E.button,{whileHover:r||i?{}:{scale:1.02},whileTap:r||i?{}:{scale:.98},transition:{duration:.2},className:"w-full py-3 px-4 rounded-md font-medium text-center transition-colors ".concat(r?"bg-gray-300 text-gray-500 cursor-not-allowed":i?"bg-indigo-500 text-white cursor-wait":"bg-indigo-600 text-white hover:bg-indigo-700"),onClick:u,disabled:r||i,children:i?(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsx)(v.Z,{className:"animate-spin h-4 w-4 mr-2"}),l]}):c}),d&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded-md",children:(0,a.jsx)("p",{className:"text-xs text-red-600",children:d})})]})},k=r(14362),E=r(18686),I=r(86366);let Z=e=>{var t;let{item:r,updateQuantity:s,removeFromCart:l,formatPrice:i}=e;return(0,a.jsxs)("li",{className:"flex gap-4 py-4 border-b",children:[(0,a.jsx)("div",{className:"relative h-20 w-20 bg-gray-100 flex-shrink-0",children:(null===(t=r.image)||void 0===t?void 0:t.url)&&(0,a.jsx)(c.default,{src:r.image.url,alt:r.image.altText||r.name,fill:!0,sizes:"80px",className:"object-cover",priority:!1})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)("h4",{className:"text-sm font-medium line-clamp-2",children:r.name}),r.attributes&&r.attributes.length>0&&(0,a.jsx)("div",{className:"mt-1 text-xs text-gray-500",children:r.attributes.map((e,t)=>(0,a.jsxs)("span",{children:[e.name,": ",e.value,t<r.attributes.length-1?", ":""]},e.name))}),(0,a.jsx)("div",{className:"mt-1 text-sm font-medium",children:r.price&&"string"==typeof r.price&&r.price.toString().includes("₹")?r.price:"".concat(N.J6).concat(i(r.price||"0"))}),(0,a.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>{r.quantity>1&&s(r.id,r.quantity-1)},disabled:r.quantity<=1,className:"px-2 py-1 hover:bg-gray-100 disabled:opacity-50","aria-label":"Decrease quantity",children:(0,a.jsx)(p.Z,{className:"h-3 w-3"})}),(0,a.jsx)("span",{className:"px-2 py-1 text-sm",children:r.quantity}),(0,a.jsx)("button",{onClick:()=>{s(r.id,r.quantity+1)},className:"px-2 py-1 hover:bg-gray-100","aria-label":"Increase quantity",children:(0,a.jsx)(g.Z,{className:"h-3 w-3"})})]}),(0,a.jsx)("button",{onClick:()=>{l(r.id)},className:"p-1 hover:bg-gray-100 rounded-full","aria-label":"Remove item",children:(0,a.jsx)(f.Z,{className:"h-4 w-4 text-gray-500"})})]})]})]})};var q=()=>{let{isOpen:e,toggleCart:t}=(0,E.j)(),[r,c]=(0,s.useState)(!1),[p,g]=(0,s.useState)(null),[f,N]=(0,s.useState)(!1),[v,q]=(0,s.useState)(!1),[S,F]=(0,s.useState)({}),P=(0,l.useRouter)(),{isAuthenticated:T,user:R,token:z}=(0,k.a)(),D=(0,y.useLocalCartStore)(),{items:Q,itemCount:A,removeCartItem:Y,updateCartItem:_,clearCart:L,error:M,setError:O}=D,$=e=>{try{let t="string"==typeof e?parseFloat(e):e;if(isNaN(t))return"0.00";return t.toFixed(2)}catch(e){return console.error("Error formatting price:",e),"0.00"}};(0,s.useEffect)(()=>{console.log("Cart items:",Q),console.log("Cart subtotal calculation:");let e=0;Q.forEach(t=>{let r=0,a=(r="string"==typeof t.price?parseFloat(t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"")):t.price)*t.quantity;console.log("Item: ".concat(t.name,", Price: ").concat(t.price,", Cleaned price: ").concat(r,", Quantity: ").concat(t.quantity,", Total: ").concat(a)),e+=a}),console.log("Manual subtotal calculation: ".concat(e)),console.log("Store subtotal calculation: ".concat(D.subtotal()))},[Q,D]);let H=$(Q.reduce((e,t)=>{let r=0;return isNaN(r="string"==typeof t.price?parseFloat(t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"")):t.price)?(console.warn("Invalid price for item ".concat(t.id,": ").concat(t.price)),e):e+r*t.quantity},0));(0,s.useEffect)(()=>{(async()=>{let e={};for(let r of Q)try{if(!S[r.productId])try{let t=await j.gk(r.productId);(null==t?void 0:t.slug)?e[r.productId]=t.slug:(console.warn("Product with ID ".concat(r.productId," has no slug")),e[r.productId]="product-not-found")}catch(a){var t;console.error("Failed to load handle for product ".concat(r.productId,":"),a),e[r.productId]="product-not-found",(null===(t=a.message)||void 0===t?void 0:t.includes("No product ID was found"))&&console.warn("Product with ID ".concat(r.productId," not found in WooCommerce, but keeping in cart"))}}catch(e){console.error("Error processing product ".concat(r.productId,":"),e)}Object.keys(e).length>0&&F(t=>({...t,...e}))})()},[Q,S]);let J=async(e,t)=>{N(!0);try{await _(e,t),I.Q5.itemUpdated(e,t,"Item quantity updated")}catch(t){console.error("Error updating quantity:",t);let e=t instanceof Error?t.message:"Failed to update quantity";O(e),I.Cc.show(e,"error")}finally{N(!1)}},U=async e=>{try{await Y(e),I.Q5.itemRemoved(e,"Item removed from cart")}catch(t){console.error("Error removing item:",t);let e=t instanceof Error?t.message:"Failed to remove item";O(e),I.Cc.show(e,"error")}},W=async()=>{try{await L(),I.Q5.cleared("Cart cleared")}catch(t){console.error("Error clearing cart:",t);let e=t instanceof Error?t.message:"Failed to clear cart";I.Cc.show(e,"error")}},B=async()=>{c(!0),g(null);try{if(0===Q.length)throw Error("Your cart is empty");if(!T)throw Error("Please log in to continue with checkout");t(),P.push("/checkout")}catch(e){console.error("Checkout error:",e),g(e instanceof Error?e.message:"An error occurred during checkout"),I.Cc.show(e instanceof Error?e.message:"An error occurred during checkout","error"),c(!1)}},G=async()=>{q(!0),g(null);try{await B()}catch(e){console.error("Retry error:",e),g(e instanceof Error?e.message:"Retry failed")}finally{q(!1)}},K=Q.length>0;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.M,{children:e&&(0,a.jsx)(n.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:t,className:"fixed inset-0 bg-black/50 z-40","aria-hidden":"true"})}),(0,a.jsx)(i.M,{children:e&&(0,a.jsxs)(n.E.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"tween",ease:"easeInOut",duration:.3},className:"fixed top-0 right-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,a.jsxs)("h2",{className:"text-lg font-medium flex items-center gap-2",children:[(0,a.jsx)(o.Z,{className:"h-5 w-5"}),"Your Cart"]}),(0,a.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-full","aria-label":"Close cart",children:(0,a.jsx)(d.Z,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-4",children:[!K&&!M&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[(0,a.jsx)(o.Z,{className:"h-12 w-12 text-gray-300 mb-2"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"Looks like you haven't added any items yet."}),(0,a.jsxs)(w.z,{onClick:t,className:"flex items-center gap-2",children:["Continue Shopping",(0,a.jsx)(m.Z,{className:"h-4 w-4"})]})]}),M&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[(0,a.jsx)(u.Z,{className:"h-12 w-12 text-red-500 mb-2"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:M}),(0,a.jsxs)(w.z,{onClick:()=>O(null),className:"flex items-center gap-2",variant:"outline",children:[(0,a.jsx)(x.Z,{className:"h-4 w-4"}),"Try Again"]})]}),K&&(0,a.jsx)("ul",{className:"divide-y",children:Q.map(e=>(0,a.jsx)(Z,{item:e,updateQuantity:J,removeFromCart:U,formatPrice:$},e.id))})]}),(0,a.jsxs)("div",{className:"border-t p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsxs)("span",{children:["₹",H]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",H]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(C,{onClick:B,isDisabled:!K||f,text:"Proceed to Checkout",loadingText:"Preparing Checkout..."})}),p&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 p-3 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(u.Z,{className:"h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-red-700",children:p}),(0,a.jsx)("button",{onClick:G,disabled:v,className:"mt-2 text-xs flex items-center text-red-700 hover:text-red-800",children:v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.Z,{className:"h-3 w-3 animate-spin mr-1"}),"Retrying..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.Z,{className:"h-3 w-3 mr-1"}),"Try again"]})})]})]})}),!navigator.onLine&&(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 p-3 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 text-yellow-500 mr-2"}),(0,a.jsx)("p",{className:"text-xs text-yellow-700",children:"You appear to be offline. Please check your internet connection."})]})})]}),(0,a.jsx)("button",{onClick:W,className:"w-full text-center text-gray-500 text-sm mt-2 hover:text-gray-700",disabled:r||f,children:"Clear Cart"})]})})]})}}}]);