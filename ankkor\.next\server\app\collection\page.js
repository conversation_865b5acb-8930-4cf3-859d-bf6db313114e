(()=>{var e={};e.id=1306,e.ids=[1306],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},11443:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.Z,__next_app__:()=>m,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>l}),r(32606),r(11360),r(7629),r(11930),r(12523);var n=r(23191),o=r(88716),s=r(43315),a=r(95231),i={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);r.d(t,i);let l=["",{children:["collection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32606)),"E:\\ankkorwoo\\ankkor\\src\\app\\collection\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\collection\\page.tsx"],d="/collection/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/collection/page",pathname:"/collection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33074:(e,t,r)=>{Promise.resolve().then(r.bind(r,90754))},41137:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(69029),o=r.n(n)},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return i}});let n=r(91174),o=r(23078),s=r(92481),a=n._(r(86820));function i(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},90754:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>g});var o=r(10326),s=r(17577),a=r(46226),i=r(41137),l=r(94019),c=r(53471),d=r(15725),m=r(9512),p=r(45107),u=e([c,d]);[c,d]=u.then?(await u)():u;let x=[{id:"all",name:"All Categories"}],h=[{id:"featured",name:"Featured"},{id:"newest",name:"Newest"},{id:"price-asc",name:"Price: Low to High"},{id:"price-desc",name:"Price: High to Low"},{id:"rating",name:"Alphabetical"}];function g(){let[e,t]=(0,s.useState)([]),[r,n]=(0,s.useState)(!0),[d,u]=(0,s.useState)(null),[g,f]=(0,s.useState)("all"),[y,v]=(0,s.useState)("featured"),[b,k]=(0,s.useState)(!1);(0,m.Z)(r,"fabric");let j=e=>{let t=(e.collections||[]).map(e=>e.handle?.toLowerCase()||""),r=e._originalWooProduct,n=r?.type?.toLowerCase()||"",o=(r?.productCategories?.nodes||[]).map(e=>e.name?.toLowerCase()||"");return t.some(e=>e.includes("shirt"))||o.some(e=>e.includes("shirt"))||n.includes("shirt")?"shirts":t.some(e=>e.includes("polo"))||o.some(e=>e.includes("polo"))||n.includes("polo")?"polos":"other"},w=[..."all"===g?e:e.filter(e=>j(e)===g)].sort((e,t)=>{switch(y){case"price-asc":let r=parseFloat(e.priceRange?.minVariantPrice?.amount||"0"),n=parseFloat(t.priceRange?.minVariantPrice?.amount||"0");return r-n;case"price-desc":let o=parseFloat(e.priceRange?.minVariantPrice?.amount||"0");return parseFloat(t.priceRange?.minVariantPrice?.amount||"0")-o;case"rating":return e.title.localeCompare(t.title);case"newest":return t.id.localeCompare(e.id);default:return 0}});return(0,o.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[o.jsx("div",{className:"container mx-auto px-4 mb-12",children:(0,o.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[o.jsx("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"The Collection"}),o.jsx("p",{className:"text-[#5c5c52] mb-8",children:"Discover our curated selection of timeless menswear essentials, crafted with exceptional materials and meticulous attention to detail."})]})}),(0,o.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[o.jsx(a.default,{src:"https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?q=80",alt:"Ankkor Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),o.jsx("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center text-white",children:[o.jsx("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Spring/Summer 2025"}),o.jsx("p",{className:"text-lg max-w-xl mx-auto",children:"Timeless elegance for the modern gentleman"})]})})]}),(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[d&&(0,o.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded",children:[o.jsx("p",{children:d}),o.jsx("p",{className:"text-sm mt-2",children:"Please check your WooCommerce configuration in the .env.local file."})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center mb-8 md:hidden",children:[(0,o.jsxs)("button",{onClick:()=>k(!0),className:"flex items-center gap-2 text-[#2c2c27] border border-[#e5e2d9] px-4 py-2",children:[o.jsx(i.Z,{className:"h-4 w-4"}),o.jsx("span",{children:"Filter & Sort"})]}),(0,o.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[w.length," products"]})]}),b&&(0,o.jsxs)("div",{className:"fixed inset-0 z-50 md:hidden",children:[o.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:()=>k(!1)}),(0,o.jsxs)("div",{className:"absolute right-0 top-0 bottom-0 w-80 bg-[#f8f8f5] p-6 overflow-auto",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[o.jsx("h3",{className:"font-serif text-lg text-[#2c2c27]",children:"Filter & Sort"}),o.jsx("button",{onClick:()=>k(!1),children:o.jsx(l.Z,{className:"h-5 w-5 text-[#2c2c27]"})})]}),(0,o.jsxs)("div",{children:[o.jsx("h4",{className:"text-[#8a8778] text-xs uppercase tracking-wider mb-4",children:"Sort By"}),o.jsx("div",{className:"space-y-3",children:h.map(e=>o.jsx("button",{onClick:()=>v(e.id),className:`block w-full text-left py-1 ${y===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52]"}`,children:e.name},e.id))})]}),o.jsx("button",{onClick:()=>k(!1),className:"w-full bg-[#2c2c27] text-[#f4f3f0] py-3 mt-8 text-sm uppercase tracking-wider",children:"Apply Filters"})]})]}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-10",children:[o.jsx("div",{className:"hidden md:block w-64 shrink-0",children:o.jsx("div",{className:"sticky top-24",children:(0,o.jsxs)("div",{children:[o.jsx("h3",{className:"text-[#2c2c27] font-serif text-lg mb-6",children:"Sort By"}),o.jsx("div",{className:"space-y-3",children:h.map(e=>o.jsx("button",{onClick:()=>v(e.id),className:`block w-full text-left py-1 ${y===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52] hover:text-[#2c2c27] transition-colors"}`,children:e.name},e.id))})]})})}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[o.jsx("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"all"===g?"All Products":x.find(e=>e.id===g)?.name}),(0,o.jsxs)("div",{className:"text-[#5c5c52]",children:[w.length," products"]})]}),o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:w.map(e=>{let t="";try{if(e.variants&&e.variants.length>0){let r=e.variants[0];if(r&&r.id&&!(t=r.id).startsWith("gid://woocommerce/ProductVariant/")){let r=t.replace(/\D/g,"");r?t=`gid://woocommerce/ProductVariant/${r}`:(console.warn(`Cannot parse variant ID for product ${e.title}: ${t}`),t="")}}if(!t&&e.id&&e.id.includes("/")){let r=e.id.split("/"),n=r[r.length-1];n&&/^\d+$/.test(n)&&(t=`gid://woocommerce/ProductVariant/${n}1`,console.warn(`Using fallback variant ID for product ${e.title}: ${t}`))}let r=e._originalWooProduct;return o.jsx(c.Z,{id:e.id,name:e.title,slug:e.handle,price:r?.salePrice||r?.price||e.priceRange?.minVariantPrice?.amount||"0",image:e.images[0]?.url||"",isNew:!0,stockStatus:r?.stockStatus||"IN_STOCK",compareAtPrice:e.compareAtPrice,regularPrice:r?.regularPrice,salePrice:r?.salePrice,onSale:r?.onSale||!1,currencySymbol:(0,p.jK)(e.currencyCode),currencyCode:e.currencyCode||"INR",shortDescription:r?.shortDescription,type:r?.type},e.id)}catch(t){return console.error(`Error processing product ${e.title||"unknown"}:`,t),null}})})]})]})]})]})}n()}catch(e){n(e)}})},9512:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o}),r(17577);var n=r(2861);let o=function(e,t){let{setLoading:r,setVariant:o}=(0,n.r)()}},92079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{addInventoryMapping:()=>d,clearInventoryMappings:()=>g,getAllInventoryMappings:()=>u,getProductHandleFromInventory:()=>m,loadInventoryMap:()=>l,saveInventoryMap:()=>c,updateInventoryMappings:()=>p});var n=r(78578);let o="inventory:mapping:",s=new n.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),a={};function i(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function l(){if(!i())return{...a};try{let e=await s.keys(`${o}*`);if(0===e.length)return console.log("No existing inventory mappings found in Redis"),{};let t={},r=await s.mget(...e);return e.forEach((e,n)=>{let s=e.replace(o,""),a=r[n];t[s]=a}),console.log(`Loaded inventory mapping with ${Object.keys(t).length} entries from Redis`),t}catch(e){return console.error("Error loading inventory mapping from Redis:",e),console.log("Falling back to in-memory storage"),{...a}}}async function c(e){if(i())try{let t=s.pipeline(),r=await s.keys(`${o}*`);r.length>0&&t.del(...r),Object.entries(e).forEach(([e,r])=>{t.set(`${o}${e}`,r)}),await t.exec(),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to Redis`)}catch(t){console.error("Error saving inventory mapping to Redis:",t),console.log("Falling back to in-memory storage"),Object.assign(a,e)}else Object.assign(a,e),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to memory`)}async function d(e,t){try{return i()?(await s.set(`${o}${e}`,t),console.log(`Added mapping to Redis: ${e} -> ${t}`)):(a[e]=t,console.log(`Added mapping to memory: ${e} -> ${t}`)),!0}catch(r){console.error("Error adding inventory mapping:",r);try{return a[e]=t,console.log(`Added mapping to memory fallback: ${e} -> ${t}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function m(e){try{if(i())return await s.get(`${o}${e}`)||null;return a[e]||null}catch(t){console.error("Error getting product handle from Redis:",t);try{return a[e]||null}catch(e){return console.error("Error getting from memory fallback:",e),null}}}async function p(e){try{if(i()){let t=s.pipeline();for(let{inventoryItemId:r,productHandle:n}of e)t.set(`${o}${r}`,n);await t.exec(),console.log(`Updated ${e.length} inventory mappings in Redis`)}else{for(let{inventoryItemId:t,productHandle:r}of e)a[t]=r;console.log(`Updated ${e.length} inventory mappings in memory`)}return!0}catch(t){console.error("Error updating inventory mappings in Redis:",t);try{for(let{inventoryItemId:t,productHandle:r}of e)a[t]=r;return console.log(`Updated ${e.length} inventory mappings in memory fallback`),!0}catch(e){return console.error("Error updating in memory fallback:",e),!1}}}async function u(){return await l()}async function g(){try{if(i()){let e=await s.keys(`${o}*`);e.length>0&&await s.del(...e),console.log("Cleared all inventory mappings from Redis")}else Object.keys(a).forEach(e=>{delete a[e]}),console.log("Cleared all inventory mappings from memory");return!0}catch(e){return console.error("Error clearing inventory mappings:",e),!1}}},45107:(e,t,r)=>{"use strict";function n(e="INR"){switch(e){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return e}}r.d(t,{jK:()=>n}),r(92079)},32606:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\collection\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1057,2481,8578,5436,5725,5916,3471],()=>r(11443));module.exports=n})();