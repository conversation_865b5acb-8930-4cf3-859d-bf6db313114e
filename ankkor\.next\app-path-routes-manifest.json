{"/about/craftsmanship/page": "/about/craftsmanship", "/_not-found/page": "/_not-found", "/about/page": "/about", "/api/admin/migrate-inventory-mappings/route": "/api/admin/migrate-inventory-mappings", "/admin/products/page": "/admin/products", "/api/auth/route": "/api/auth", "/about/sustainability/page": "/about/sustainability", "/api/auth/update-profile/route": "/api/auth/update-profile", "/api/cache/products/[handle]/route": "/api/cache/products/[handle]", "/api/checkout/route": "/api/checkout", "/api/graphql/route": "/api/graphql", "/api/products/validate-stock/route": "/api/products/validate-stock", "/api/razorpay/create-order/route": "/api/razorpay/create-order", "/api/products/[id]/stock/route": "/api/products/[id]/stock", "/api/razorpay/verify-payment/route": "/api/razorpay/verify-payment", "/api/razorpay/test-order/route": "/api/razorpay/test-order", "/api/reconcile/route": "/api/reconcile", "/api/razorpay/test-connection/route": "/api/razorpay/test-connection", "/api/revalidate/route": "/api/revalidate", "/api/test/route": "/api/test", "/api/reservations/cleanup/route": "/api/reservations/cleanup", "/api/reservations/route": "/api/reservations", "/api/stock-updates/route": "/api/stock-updates", "/api/shipping-rates/route": "/api/shipping-rates", "/api/trigger-test/route": "/api/trigger-test", "/api/user/wishlist/route": "/api/user/wishlist", "/api/webhooks/order/route": "/api/webhooks/order", "/api/webhooks/inventory/route": "/api/webhooks/inventory", "/api/webhooks/route": "/api/webhooks", "/api/webhooks/simple/route": "/api/webhooks/simple", "/api/webhooks/test/route": "/api/webhooks/test", "/cart-test/page": "/cart-test", "/category/[slug]/page": "/category/[slug]", "/checkout/page": "/checkout", "/customer-service/contact/page": "/customer-service/contact", "/collection/page": "/collection", "/customer-service/faq/page": "/customer-service/faq", "/collection/polos/page": "/collection/polos", "/collection/shirts/page": "/collection/shirts", "/customer-service/page": "/customer-service", "/customer-service/size-guide/page": "/customer-service/size-guide", "/local-cart-test/page": "/local-cart-test", "/privacy-policy/page": "/privacy-policy", "/order-confirmed/page": "/order-confirmed", "/product/[slug]/page": "/product/[slug]", "/page": "/", "/robots.txt/route": "/robots.txt", "/return-policy/page": "/return-policy", "/shipping-policy/page": "/shipping-policy", "/search/page": "/search", "/terms-of-service/page": "/terms-of-service", "/test-auth/page": "/test-auth", "/test-auth/success/page": "/test-auth/success", "/test-woo/page": "/test-woo", "/woocommerce-cart-test/page": "/woocommerce-cart-test", "/test/page": "/test", "/woocommerce-checkout-test/page": "/woocommerce-checkout-test", "/wishlist/page": "/wishlist", "/woocommerce-test/page": "/woocommerce-test", "/woocommerce-test/success/page": "/woocommerce-test/success", "/sitemap.xml/route": "/sitemap.xml", "/account/page": "/account", "/api/auth/me/route": "/api/auth/me", "/api/ankkor/v1/nonce/route": "/api/ankkor/v1/nonce", "/api/cron/inventory-sync/route": "/api/cron/inventory-sync", "/api/auth/user/route": "/api/auth/user", "/api/nonce/route": "/api/nonce", "/api/debug/route": "/api/debug", "/api/products/route": "/api/products", "/categories/page": "/categories", "/api/woo-sync/route": "/api/woo-sync", "/sign-up/page": "/sign-up", "/sign-in/page": "/sign-in"}